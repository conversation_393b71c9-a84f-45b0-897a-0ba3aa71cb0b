日志已清空 - 2025-08-09 02:46:50
--------------------------------------------------------------------------------
[2025-08-10 01:49:15] [ERROR] 同步字幕处理失败: 'Communicate' object has no attribute 'text'
异常类型: AttributeError
异常信息: 'Communicate' object has no attribute 'text'
堆栈跟踪:
  Traceback (most recent call last):
    File "d:\jiedan\7.5 AI配音\modules\voice_tts\core.py", line 1759, in save_with_subtitles_sync
    original_text = communicate.text
  AttributeError: 'Communicate' object has no attribute 'text'
--------------------------------------------------------------------------------
[2025-08-10 01:49:17] [ERROR] 同步字幕处理失败: 'Communicate' object has no attribute 'text'
异常类型: AttributeError
异常信息: 'Communicate' object has no attribute 'text'
堆栈跟踪:
  Traceback (most recent call last):
    File "d:\jiedan\7.5 AI配音\modules\voice_tts\core.py", line 1759, in save_with_subtitles_sync
    original_text = communicate.text
  AttributeError: 'Communicate' object has no attribute 'text'
--------------------------------------------------------------------------------
[2025-08-10 01:49:19] [ERROR] 同步字幕处理失败: 'Communicate' object has no attribute 'text'
异常类型: AttributeError
异常信息: 'Communicate' object has no attribute 'text'
堆栈跟踪:
  Traceback (most recent call last):
    File "d:\jiedan\7.5 AI配音\modules\voice_tts\core.py", line 1759, in save_with_subtitles_sync
    original_text = communicate.text
  AttributeError: 'Communicate' object has no attribute 'text'
--------------------------------------------------------------------------------
[2025-08-10 01:49:21] [ERROR] 同步字幕处理失败: 'Communicate' object has no attribute 'text'
异常类型: AttributeError
异常信息: 'Communicate' object has no attribute 'text'
堆栈跟踪:
  Traceback (most recent call last):
    File "d:\jiedan\7.5 AI配音\modules\voice_tts\core.py", line 1759, in save_with_subtitles_sync
    original_text = communicate.text
  AttributeError: 'Communicate' object has no attribute 'text'
--------------------------------------------------------------------------------
[2025-08-10 01:49:21] [ERROR] 分段处理失败 - 分段 1
分段文本: 来源来自网络
请于下载后24小时内删除
内容版权归作者所有
如不慎该文本侵犯了您的权益
请麻烦通知我们及时删除
谢谢
机场人来人往
广播上有女子清甜的声音报着航班
当加州到达A市的飞机降落
旅客们一窝...
错误信息: 达到最大重试次数，所有尝试都失败
--------------------------------------------------------------------------------
[2025-08-10 01:51:55] [ERROR] TTS处理失败
文本: 来源来自网络
请于下载后24小时内删除
语音: zh-CN-XiaoxiaoNeural
语速: 0
音调: 0
音量: 0
错误信息: edge-tts处理失败: Invalid rate '0'.
异常类型: ValueError
异常信息: Invalid rate '0'.
堆栈跟踪:
  Traceback (most recent call last):
    File "D:\jiedan\7.5 AI配音\modules\voice_tts\core.py", line 1644, in text_to_speech_cli
    communicate = edge_tts.Communicate(**communicate_kwargs)
    File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\edge_tts\communicate.py", line 350, in __init__
    self.tts_config = TTSConfig(voice, rate, volume, pitch)
    File "<string>", line 7, in __init__
    File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\edge_tts\data_classes.py", line 71, in __post_init__
    self.validate_string_param("rate", self.rate, r"^[+-]\d+%$")
    File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\edge_tts\data_classes.py", line 37, in validate_string_param
    raise ValueError(f"Invalid {param_name} '{param_value}'.")
  ValueError: Invalid rate '0'.
--------------------------------------------------------------------------------
