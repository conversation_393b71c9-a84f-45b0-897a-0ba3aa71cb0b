"""
基于FFmpeg的视频合成器
替代MoviePy以提升处理速度和减少依赖
"""

import os
import sys
import json
import subprocess
import tempfile
import threading
import time
import re
import shutil
import platform
from pathlib import Path
from datetime import datetime

# 导入进程管理器
try:
    from .core import video_ffmpeg_manager
except ImportError:
    # 如果导入失败，创建一个简单的占位符
    class DummyManager:
        def register_process(self, process): pass
        def unregister_process(self, process): pass
    video_ffmpeg_manager = DummyManager()

def create_managed_ffmpeg_process(cmd):
    """创建一个被管理的FFmpeg进程"""
    if platform.system() == "Windows":
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                 text=True, encoding='utf-8', errors='ignore',
                                 creationflags=subprocess.CREATE_NO_WINDOW)
    else:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                 text=True, encoding='utf-8', errors='ignore')

    # 注册进程到管理器
    video_ffmpeg_manager.register_process(process)
    return process

def cleanup_managed_process(process):
    """清理被管理的FFmpeg进程"""
    if process:
        video_ffmpeg_manager.unregister_process(process)


def get_long_path(path):
    """获取Windows长路径格式"""
    if os.name == 'nt' and not path.startswith('\\\\?\\'):
        return f'\\\\?\\{os.path.abspath(path)}'
    return path


class FFmpegVideoComposer:
    """基于FFmpeg的视频合成器"""

    def __init__(self, temp_manager=None):
        self.should_stop = False
        self.progress_callback = None
        # 循环视频缓存：{(video_path, duration, settings_hash): cached_video_path}
        self.loop_video_cache = {}
        # 缓存目录
        self.cache_dir = None
        # 临时文件管理器
        self.temp_manager = temp_manager

    def is_available(self):
        """检查FFmpeg是否可用"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        return ffmpeg_exe is not None

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

    def _get_ffmpeg_executable(self):
        """获取FFmpeg可执行文件路径"""
        # 尝试从配置文件获取FFmpeg路径
        config_files = [
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config.json"),
            "config.json"
        ]

        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                    if 'ffmpeg_path' in config and config['ffmpeg_path']:
                        ffmpeg_dir = config['ffmpeg_path']

                        # 尝试多种可能的路径
                        possible_paths = [
                            os.path.join(ffmpeg_dir, "ffmpeg.exe"),
                            os.path.join(ffmpeg_dir, "bin", "ffmpeg.exe"),
                            ffmpeg_dir if ffmpeg_dir.endswith('ffmpeg.exe') else None
                        ]

                        for path in possible_paths:
                            if path and os.path.exists(path):
                                return path
                except Exception:
                    continue

        # 尝试系统PATH
        try:
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True, encoding='utf-8', errors='ignore')
            if result.returncode == 0:
                return 'ffmpeg'
        except Exception:
            pass

        return None

    def get_media_duration(self, file_path):
        """获取媒体文件时长（秒）"""
        ffprobe_exe = self._get_ffprobe_executable()
        if not ffprobe_exe:
            raise Exception("FFprobe不可用")

        cmd = [
            ffprobe_exe, '-v', 'quiet',
            '-show_entries', 'format=duration',
            '-of', 'csv=p=0',
            file_path
        ]

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', check=True)
            return float(result.stdout.strip())
        except Exception as e:
            raise Exception(f"获取媒体时长失败: {e}")

    def _get_ffprobe_executable(self):
        """获取FFprobe可执行文件路径"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            return None

        if ffmpeg_exe == 'ffmpeg':
            return 'ffprobe'
        else:
            # 构建ffprobe路径
            ffprobe_exe = ffmpeg_exe.replace('ffmpeg.exe', 'ffprobe.exe')
            if os.path.exists(ffprobe_exe):
                return ffprobe_exe

            # 尝试其他可能的路径
            ffmpeg_dir = os.path.dirname(ffmpeg_exe)
            ffprobe_exe = os.path.join(ffmpeg_dir, 'ffprobe.exe')
            if os.path.exists(ffprobe_exe):
                return ffprobe_exe

            # 尝试不带.exe的版本（Linux/Mac）
            ffprobe_exe = ffmpeg_exe.replace('ffmpeg', 'ffprobe')
            if os.path.exists(ffprobe_exe):
                return ffprobe_exe

            return None

    def loop_video_to_duration(self, video_path, target_duration, output_path, settings=None, progress_callback=None):
        """将视频循环到指定时长"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        # 获取原视频时长
        video_duration = self.get_media_duration(video_path)

        # 计算需要循环的次数
        loop_count = max(1, int(target_duration / video_duration) + 1)

        if progress_callback:
            progress_callback(f"视频时长: {video_duration:.2f}秒，目标: {target_duration:.2f}秒")
            progress_callback(f"需要循环 {loop_count} 次")

        # 确保设置了正确的码率（如果启用了自定义压缩）
        if settings and settings.get('custom_compression', False):
            if 'current_compression_bitrate' not in settings:
                default_bitrate = settings.get('first_compression_bitrate', 5000)
                settings['current_compression_bitrate'] = default_bitrate
                if progress_callback:
                    progress_callback(f"🔧 循环视频设置码率: {default_bitrate}k (使用第一集码率)")

        # 检查是否需要在循环时应用编码设置
        need_encoding = self._need_video_encoding_for_loop(settings)

        if progress_callback:
            if need_encoding:
                progress_callback(f"🔍 使用编码循环模式（会应用编码设置）")
            else:
                progress_callback(f"🔍 使用简单拼接模式（直接复制，最快）")

        if need_encoding:
            # 检查是否应该使用预编码优化
            if self._should_use_pre_encoding(settings):
                if progress_callback:
                    progress_callback("🚀 使用预编码优化模式（先编码后循环）")
                result = self._loop_video_with_pre_encoding(video_path, target_duration, output_path, settings, progress_callback)
            else:
                # 需要编码的循环（应用码率等设置）
                result = self._loop_video_with_encoding(video_path, target_duration, output_path, loop_count, settings, progress_callback)
        else:
            # 简单的复制循环（最快）
            result = self._loop_video_simple_concat(video_path, target_duration, output_path, loop_count, settings, progress_callback)

        # 验证循环结果
        if result and result != "USER_STOPPED" and os.path.exists(output_path):
            try:
                actual_duration = self.get_media_duration(output_path)
                if progress_callback:
                    progress_callback(f"🔍 循环完成，实际时长: {actual_duration:.2f}秒，目标时长: {target_duration:.2f}秒")
                    if abs(actual_duration - target_duration) > 5:  # 如果差异超过5秒
                        progress_callback(f"⚠️ 警告：循环视频时长与目标时长差异较大！差异: {abs(actual_duration - target_duration):.2f}秒")
            except Exception as e:
                if progress_callback:
                    progress_callback(f"⚠️ 获取循环视频时长失败: {e}")

        return result

    def _loop_video_simple_concat(self, video_path, target_duration, output_path, loop_count, settings, progress_callback):
        """使用简单的concat方法循环视频 - 直接复制拼接（带随机开头）"""
        ffmpeg_exe = self._get_ffmpeg_executable()

        # 获取原视频时长
        video_duration = self.get_media_duration(video_path)

        # 生成1-5秒内的随机起始位置
        import random
        max_start_offset = min(5.0, video_duration - 1.0)  # 确保不超过视频时长
        random_start = random.uniform(0.0, max_start_offset)

        if progress_callback:
            progress_callback(f"原视频时长: {video_duration:.2f}秒，随机起始位置: {random_start:.2f}秒")

        # 创建临时进度文件
        progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
        with open(progress_file_path, 'w') as f:
            pass  # 创建空文件

        # 使用stream_loop + 随机起始位置（简单拼接版本）
        cmd = [
            ffmpeg_exe, '-y',
            '-progress', progress_file_path,
            '-ss', str(random_start),  # 随机起始位置
            '-stream_loop', '-1',  # 无限循环
            '-i', video_path,
            '-t', str(target_duration),  # 精确裁剪到目标时长
            '-c', 'copy',  # 直接复制，不重新编码
            '-avoid_negative_ts', 'make_zero',
            '-fflags', '+genpts+igndts',  # 重新生成时间戳并忽略DTS
            '-movflags', '+faststart',  # 优化播放兼容性和跳转
            output_path
        ]

        if progress_callback:
            progress_callback("正在快速拼接视频（随机开头）...")

            # 启动FFmpeg进程（隐藏窗口）
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback:
                self._monitor_ffmpeg_progress(progress_file_path, target_duration, progress_callback, "拼接视频")

            # 等待进程完成
            stdout, stderr = process.communicate()



            if process.returncode != 0:
                # 检查是否是用户手动中止
                if self._is_user_terminated(process.returncode, stderr):
                    if progress_callback:
                        progress_callback("视频循环已停止")
                    return "USER_STOPPED"  # 返回特殊值表示用户中止
                else:
                    # 只有在不是用户中止的情况下才尝试重新编码
                    if progress_callback:
                        progress_callback(f"快速拼接失败，使用重新编码方式: {stderr}")
                    # 如果直接copy失败，使用重新编码
                    return self._loop_video_with_reencoding(video_path, target_duration, output_path, loop_count, settings, progress_callback)

            if progress_callback:
                progress_callback("视频循环完成")

            return True

        finally:
            # 注销进程
            if 'process' in locals():
                video_ffmpeg_manager.unregister_process(process)

            # 清理临时文件
            try:
                os.unlink(progress_file_path)
            except Exception:
                pass

    def _loop_video_with_reencoding(self, video_path, target_duration, output_path, loop_count, settings, progress_callback):
        """使用重新编码的方式循环视频（备用方案）"""
        ffmpeg_exe = self._get_ffmpeg_executable()

        # 创建临时concat文件
        concat_file_path = self._create_temp_file(suffix='.txt', prefix='concat_')
        concat_file = open(concat_file_path, 'w', encoding='utf-8')

        try:
            # 写入concat文件内容
            for i in range(loop_count):
                abs_video_path = os.path.abspath(video_path).replace('\\', '/')
                concat_file.write(f"file '{abs_video_path}'\n")
            concat_file.close()

            # 创建临时进度文件
            progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
            with open(progress_file_path, 'w') as f:
                pass  # 创建空文件

            # 使用重新编码确保兼容性
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file_path,
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_file.name,
                '-t', str(target_duration),
                '-c:v', 'libx264',
                '-preset', 'fast',  # 使用fast预设提高速度
                '-crf', '23',  # 合理的质量
                '-pix_fmt', 'yuv420p',
                '-c:a', 'aac',
                '-b:a', '128k',
                '-movflags', '+faststart',
                output_path
            ]

            if progress_callback:
                progress_callback("正在重新编码拼接视频...")

            # 启动FFmpeg进程（隐藏窗口）
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 监控进度
            if progress_callback:
                self._monitor_ffmpeg_progress(progress_file_path, target_duration, progress_callback, "重编码拼接")

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                # 检查是否是用户手动中止
                if self._is_user_terminated(process.returncode, stderr):
                    if progress_callback:
                        progress_callback("视频重编码已停止")
                    return "USER_STOPPED"  # 返回特殊值表示用户中止
                else:
                    # 只有在不是用户中止的情况下才报告错误
                    if progress_callback:
                        progress_callback(f"重新编码失败: {stderr}")
                    return False

            if progress_callback:
                progress_callback("视频循环完成")

            return True

        finally:
            # 清理临时文件
            try:
                os.unlink(concat_file.name)
                os.unlink(progress_file_path)
            except Exception:
                pass

    def _loop_video_with_filter(self, video_path, target_duration, output_path, loop_count, settings, progress_callback):
        """使用filter_complex进行视频循环（最后备用方案）"""
        if progress_callback:
            progress_callback("所有简单方法都失败了，尝试使用filter方法...")

        # 这个方法通常很慢，所以直接返回失败，让用户知道有问题
        if progress_callback:
            progress_callback("Filter方法处理时间较长，建议检查视频文件格式或使用其他视频文件")

        return False
    def _need_video_encoding_for_loop(self, settings):
        """检查循环视频时是否需要编码 - 简化版本，总是使用编码以确保设置生效"""
        # 总是使用编码方式，确保所有设置（分辨率、码率等）都能在循环时一次性应用
        # 这样可以减少后续的处理步骤，提高整体效率
        return True

    def _should_use_pre_encoding(self, settings):
        """判断是否应该使用预编码优化"""
        if not settings:
            return False

        # 检查是否有需要编码的设置
        has_encoding_settings = (
            settings.get('custom_resolution', False) or
            settings.get('custom_compression', False) or
            settings.get('force_encoding', False)
        )

        return has_encoding_settings

    def _get_pre_encoded_cache_key(self, video_path, settings):
        """生成预编码视频的缓存键"""
        import hashlib

        # 获取视频文件的基本信息
        video_stat = os.stat(video_path)
        video_info = f"{video_path}_{video_stat.st_size}_{video_stat.st_mtime}"

        # 获取编码设置的哈希（包含编码器/分辨率/码率等影响输出的关键因素）
        settings_str = ""
        if settings:
            # 分辨率
            if settings.get('custom_resolution', False):
                width = settings.get('width', 1920)
                height = settings.get('height', 1080)
                keep_ar = settings.get('keep_aspect_ratio', True)
                settings_str += f"_res_{width}x{height}_{'keep' if keep_ar else 'stretch'}"
            # 码率
            if settings.get('custom_compression', False):
                curr_br = settings.get('current_compression_bitrate', settings.get('first_compression_bitrate', 5000))
                settings_str += f"_comp_{curr_br}"
            # 强制编码
            if settings.get('force_encoding', False):
                settings_str += "_force"
            # 编码器（硬件/软件）
            enc = settings.get('encoder', 'auto')
            use_hw = settings.get('use_hardware_acceleration', True)
            enc_tag = enc if enc != 'auto' else ('h264_nvenc' if use_hw else 'libx264')
            settings_str += f"_enc_{enc_tag}"

        # 生成缓存键
        cache_key = hashlib.md5((video_info + settings_str).encode()).hexdigest()
        return f"pre_encoded_{cache_key}"

    def _pre_encode_base_video(self, video_path, settings, progress_callback=None):
        """预编码基础视频，应用所有编码设置"""
        try:
            # 检查缓存
            cache_key = self._get_pre_encoded_cache_key(video_path, settings)
            cached_video = self._get_cached_video(cache_key)

            if cached_video and os.path.exists(cached_video):
                if progress_callback:
                    progress_callback("🔄 使用缓存的预编码视频")
                return cached_video

            if progress_callback:
                progress_callback("🔧 开始预编码基础视频...")

            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return None

            # 创建临时输出文件
            temp_video = self._create_temp_file(suffix='.mp4', prefix='pre_encoded_')

            # 使用稳定的NVENC参数（基于最佳实践）
            codec, ffmpeg_params = self._get_stable_nvenc_settings(settings, progress_callback)

            # 创建临时进度文件
            progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
            with open(progress_file_path, 'w') as f:
                pass  # 创建空文件

            # 构建FFmpeg命令（使用进度文件）
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file_path,
                '-i', video_path,
                '-c:v', codec
            ]

            # 添加编码器参数（已包含分辨率和压缩设置）
            cmd.extend(ffmpeg_params)

            # 音频处理
            cmd.extend(['-c:a', 'aac', '-b:a', '128k'])

            # 优化参数（避免重复-pix_fmt）
            cmd.extend([
                '-movflags', '+faststart',
                '-avoid_negative_ts', 'make_zero',
                '-fflags', '+genpts',
                temp_video
            ])

            # 打印完整命令到控制台
            print("=" * 80)
            print("🔧 FFmpeg完整命令:")
            print(" ".join(cmd))
            print("=" * 80)

            # 执行编码并直接打印输出
            import subprocess


            print("📺 FFmpeg原版输出开始:")
            print("-" * 80)

            try:
                # 创建进程，隐藏窗口但输出到控制台
                import subprocess

                # Windows下隐藏窗口的设置
                startupinfo = None
                if os.name == 'nt':  # Windows
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = subprocess.SW_HIDE

                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    encoding='utf-8',
                    errors='ignore',
                    bufsize=1,
                    universal_newlines=True,
                    startupinfo=startupinfo
                )

                # 监控进度
                if progress_callback:
                    try:
                        video_duration = self.get_media_duration(video_path)
                        self._monitor_ffmpeg_progress(
                            progress_file_path, video_duration, progress_callback, "预编码", process
                        )
                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"⚠️ 无法启动进度监控: {e}")

                # 等待进程完成
                stdout, stderr = process.communicate()
                success = process.returncode == 0

                print("-" * 80)
                print(f"📊 FFmpeg进程结束，返回码: {process.returncode}")
                print("=" * 80)

                if success and os.path.exists(temp_video):
                    # 缓存预编码视频
                    self._cache_video(cache_key, temp_video)
                    if progress_callback:
                        progress_callback("✅ 预编码完成")
                    return temp_video
                else:
                    if progress_callback:
                        progress_callback(f"❌ 预编码失败: {stderr}")
                    # 清理失败的临时文件
                    self._cleanup_temp_file(temp_video)
                    return None

            except Exception as e:
                print(f"💥 FFmpeg执行出错: {e}")
                if progress_callback:
                    progress_callback(f"❌ 预编码失败: {e}")
                self._cleanup_temp_file(temp_video)
                return None
            finally:
                # 清理临时进度文件
                try:
                    os.unlink(progress_file_path)
                except Exception:
                    pass

        except Exception as e:
            if progress_callback:
                progress_callback(f"❌ 预编码失败: {e}")
            # 清理失败的临时文件
            if 'temp_video' in locals():
                self._cleanup_temp_file(temp_video)
            return None

    def _loop_pre_encoded_video(self, pre_encoded_video, target_duration, output_path, progress_callback=None):
        """使用预编码视频快速循环到指定时长（带随机起始位置）"""
        try:
            if progress_callback:
                progress_callback("⚡ 使用预编码视频快速循环（随机开头）...")

            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return False

            # 获取预编码视频时长
            video_duration = self.get_media_duration(pre_encoded_video)

            # 生成1-5秒内的随机起始位置
            import random
            max_start_offset = min(5.0, video_duration - 1.0)  # 确保不超过视频时长
            random_start = random.uniform(0.0, max_start_offset)

            if progress_callback:
                progress_callback(f"预编码视频时长: {video_duration:.2f}秒，随机起始位置: {random_start:.2f}秒")

            # 创建临时进度文件
            progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
            with open(progress_file_path, 'w') as f:
                pass  # 创建空文件

            # 使用stream_loop + 随机起始位置
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file_path,
                '-ss', str(random_start),  # 随机起始位置
                '-stream_loop', '-1',  # 无限循环
                '-i', pre_encoded_video,
                '-t', str(target_duration),  # 精确裁剪到目标时长
                '-c', 'copy',  # 直接复制，不重新编码
                '-avoid_negative_ts', 'make_zero',
                '-fflags', '+genpts+igndts',  # 重新生成时间戳并忽略DTS
                '-movflags', '+faststart',  # 优化播放兼容性和跳转
                output_path
            ]

            # 打印完整命令到控制台
            print("=" * 80)
            print("🔧 随机开头快速循环FFmpeg命令:")
            print(" ".join(cmd))
            print("=" * 80)

            # 执行FFmpeg命令并输出到控制台
            import subprocess

            print("📺 随机开头快速循环FFmpeg输出开始:")
            print("-" * 80)

            # 设置启动信息（Windows下隐藏控制台窗口）
            startupinfo = None
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE

            # 启动进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                encoding='utf-8',
                errors='ignore',
                startupinfo=startupinfo
            )

            try:
                # 监控进度
                if progress_callback:
                    try:
                        self._monitor_ffmpeg_progress(
                            progress_file_path, target_duration, progress_callback, "随机开头快速循环", process
                        )
                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"⚠️ 无法启动进度监控: {e}")

                # 等待进程完成
                stdout, stderr = process.communicate()
                success = process.returncode == 0

                print("-" * 80)
                print(f"📊 随机开头快速循环FFmpeg进程结束，返回码: {process.returncode}")
                print("=" * 80)

                if success and os.path.exists(output_path):
                    if progress_callback:
                        progress_callback("✅ 随机开头快速循环完成")
                    return True
                else:
                    if progress_callback:
                        progress_callback(f"❌ 随机开头快速循环失败: {stderr}")
                    return False

            except Exception as e:
                print(f"💥 随机开头快速循环FFmpeg执行出错: {e}")
                if progress_callback:
                    progress_callback(f"❌ 随机开头快速循环失败: {e}")
                return False
            finally:
                # 清理临时进度文件
                try:
                    os.unlink(progress_file_path)
                except Exception:
                    pass

        except Exception as e:
            if progress_callback:
                progress_callback(f"❌ 随机开头快速循环失败: {e}")
            return False

    def _loop_video_with_pre_encoding(self, video_path, target_duration, output_path, settings, progress_callback=None):
        """使用预编码优化的循环方法"""
        try:
            if progress_callback:
                progress_callback("🚀 开始预编码优化循环...")

            # 第1步：预编码基础视频
            pre_encoded_video = self._pre_encode_base_video(video_path, settings, progress_callback)

            if not pre_encoded_video or not os.path.exists(pre_encoded_video):
                if progress_callback:
                    progress_callback("预编码失败，回退到传统编码方式...")
                # 回退到传统方式
                video_duration = self.get_media_duration(video_path)
                loop_count = max(1, int(target_duration / video_duration) + 1)
                return self._loop_video_with_encoding(video_path, target_duration, output_path, loop_count, settings, progress_callback)

            # 第2步：使用预编码视频快速循环
            success = self._loop_pre_encoded_video(pre_encoded_video, target_duration, output_path, progress_callback)

            if not success:
                if progress_callback:
                    progress_callback("快速循环失败，回退到传统编码方式...")
                # 回退到传统方式
                video_duration = self.get_media_duration(video_path)
                loop_count = max(1, int(target_duration / video_duration) + 1)
                return self._loop_video_with_encoding(video_path, target_duration, output_path, loop_count, settings, progress_callback)

            if progress_callback:
                progress_callback("🎉 预编码优化循环完成！")

            return True

        except Exception as e:
            if progress_callback:
                progress_callback(f"预编码优化失败: {e}，回退到传统方式...")
            # 回退到传统方式
            try:
                video_duration = self.get_media_duration(video_path)
                loop_count = max(1, int(target_duration / video_duration) + 1)
                return self._loop_video_with_encoding(video_path, target_duration, output_path, loop_count, settings, progress_callback)
            except Exception as fallback_error:
                if progress_callback:
                    progress_callback(f"传统方式也失败: {fallback_error}")
                return False

    def _get_cached_video(self, cache_key):
        """获取缓存的视频文件"""
        try:
            cache_dir = self._get_cache_dir()
            if not cache_dir:
                return None

            cache_file = os.path.join(cache_dir, f"{cache_key}.mp4")
            if os.path.exists(cache_file):
                return cache_file
            return None
        except Exception:
            return None

    def _cache_video(self, cache_key, video_path):
        """缓存视频文件"""
        try:
            cache_dir = self._get_cache_dir()
            if not cache_dir:
                return False

            cache_file = os.path.join(cache_dir, f"{cache_key}.mp4")

            # 复制文件到缓存目录
            import shutil
            shutil.copy2(video_path, cache_file)
            return True
        except Exception:
            return False

    def _get_cache_dir(self):
        """获取缓存目录"""
        try:
            from common.path_utils import get_app_path
            cache_dir = os.path.join(get_app_path(), "temp", "video_composer_cache")
            os.makedirs(cache_dir, exist_ok=True)
            return cache_dir
        except Exception:
            return None

    def _get_temp_dir(self):
        """获取临时文件目录"""
        try:
            from common.path_utils import get_app_path
            temp_dir = os.path.join(get_app_path(), "temp", "video_processing")
            os.makedirs(temp_dir, exist_ok=True)
            return temp_dir
        except Exception:
            return None

    def _create_temp_file(self, suffix='.mp4', prefix='temp_'):
        """创建临时文件"""
        try:
            # 优先使用TempFileManager
            if self.temp_manager:
                return self.temp_manager.create_temp_file(suffix=suffix, prefix=prefix)

            # 回退到原有逻辑
            temp_dir = self._get_temp_dir()
            if not temp_dir:
                # 回退到系统临时目录
                import tempfile
                return tempfile.NamedTemporaryFile(suffix=suffix, prefix=prefix, delete=False).name

            import uuid
            filename = f"{prefix}{uuid.uuid4().hex}{suffix}"
            return os.path.join(temp_dir, filename)
        except Exception:
            # 最后的回退
            import tempfile
            return tempfile.NamedTemporaryFile(suffix=suffix, prefix=prefix, delete=False).name

    def _cleanup_temp_file(self, file_path):
        """清理临时文件"""
        try:
            if file_path and os.path.exists(file_path):
                os.unlink(file_path)
                return True
        except Exception:
            pass
        return False

    def cleanup_temp_directory(self):
        """清理整个临时目录"""
        try:
            temp_dir = self._get_temp_dir()
            if temp_dir and os.path.exists(temp_dir):
                import shutil
                shutil.rmtree(temp_dir)
                return True
        except Exception:
            pass
        return False

    def get_temp_directory_size(self):
        """获取临时目录大小（MB）"""
        try:
            temp_dir = self._get_temp_dir()
            if not temp_dir or not os.path.exists(temp_dir):
                return 0

            total_size = 0
            for dirpath, dirnames, filenames in os.walk(temp_dir):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except Exception:
                        continue

            return total_size / (1024 * 1024)  # 转换为MB
        except Exception:
            return 0

    def _get_video_info(self, video_path):
        """获取视频信息，包括时长、分辨率、帧率等"""
        try:
            ffprobe_exe = self._get_ffprobe_executable()
            if not ffprobe_exe:
                return {'duration': 0, 'width': 1920, 'height': 1080, 'fps': 30}

            cmd = [
                ffprobe_exe, '-v', 'quiet',
                '-select_streams', 'v:0',
                '-show_entries', 'stream=width,height,r_frame_rate,duration',
                '-show_entries', 'format=duration',
                '-of', 'csv=p=0',
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore', timeout=10)

            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                video_info = {'duration': 0, 'width': 1920, 'height': 1080, 'fps': 30}

                for line in lines:
                    parts = line.split(',')
                    if len(parts) >= 4:
                        # 视频流信息
                        try:
                            video_info['width'] = int(parts[0]) if parts[0].isdigit() else 1920
                            video_info['height'] = int(parts[1]) if parts[1].isdigit() else 1080

                            # 解析帧率
                            if '/' in parts[2]:
                                num, den = parts[2].split('/')
                                video_info['fps'] = float(num) / float(den) if float(den) > 0 else 30
                            else:
                                video_info['fps'] = float(parts[2]) if parts[2] else 30

                            # 视频流时长
                            if parts[3]:
                                video_info['duration'] = float(parts[3])
                        except (ValueError, ZeroDivisionError):
                            pass
                    elif len(parts) == 1 and parts[0]:
                        # 格式时长
                        try:
                            video_info['duration'] = float(parts[0])
                        except ValueError:
                            pass

                return video_info
        except Exception as e:
            print(f"获取视频信息失败: {e}")

        return {'duration': 0, 'width': 1920, 'height': 1080, 'fps': 30}

    def _get_video_codec_info(self, video_path):
        """获取视频文件的编码信息"""
        try:
            ffprobe_exe = self._get_ffprobe_executable()
            if not ffprobe_exe:
                return None

            cmd = [
                ffprobe_exe, '-v', 'quiet',
                '-select_streams', 'v:0',
                '-show_entries', 'stream=codec_name,profile,level,pix_fmt,width,height,r_frame_rate',
                '-of', 'csv=p=0',
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True,
                                  encoding='utf-8', errors='ignore', timeout=10)

            if result.returncode == 0 and result.stdout.strip():
                parts = result.stdout.strip().split(',')
                if len(parts) >= 7:
                    return {
                        'codec': parts[0],
                        'profile': parts[1] if parts[1] != 'unknown' else None,
                        'level': parts[2] if parts[2] != 'unknown' else None,
                        'pix_fmt': parts[3] if parts[3] != 'unknown' else None,
                        'width': int(parts[4]) if parts[4].isdigit() else None,
                        'height': int(parts[5]) if parts[5].isdigit() else None,
                        'fps': parts[6] if parts[6] != 'unknown' else None
                    }
        except Exception as e:
            print(f"获取视频编码信息失败: {e}")

        return None





    def _loop_video_with_encoding(self, video_path, target_duration, output_path, loop_count, settings, progress_callback):
        """使用编码方式循环视频（先编码后循环的优化策略）"""
        try:
            if progress_callback:
                progress_callback("🚀 使用先编码后循环的优化策略")

            # 第1步：先对原视频进行编码（应用所有设置）
            encoded_video = self._encode_base_video_for_loop(video_path, settings, progress_callback)

            if not encoded_video or not os.path.exists(encoded_video):
                if progress_callback:
                    progress_callback("编码失败，回退到传统循环编码方式...")
                # 回退到传统的循环编码方式
                return self._loop_video_with_encoding_legacy(video_path, target_duration, output_path, loop_count, settings, progress_callback)

            # 第2步：使用编码后的视频进行快速循环
            success = self._loop_encoded_video_fast(encoded_video, target_duration, output_path, progress_callback)

            if not success:
                if progress_callback:
                    progress_callback("快速循环失败，回退到传统循环编码方式...")
                # 回退到传统方式
                return self._loop_video_with_encoding_legacy(video_path, target_duration, output_path, loop_count, settings, progress_callback)

            if progress_callback:
                progress_callback("🎉 先编码后循环完成！")

            return True

        except Exception as e:
            if progress_callback:
                progress_callback(f"先编码后循环失败: {e}，回退到传统方式...")
            # 回退到传统方式
            try:
                return self._loop_video_with_encoding_legacy(video_path, target_duration, output_path, loop_count, settings, progress_callback)
            except Exception as fallback_error:
                if progress_callback:
                    progress_callback(f"传统方式也失败: {fallback_error}")
                return False

    def _encode_base_video_for_loop(self, video_path, settings, progress_callback):
        """为循环编码基础视频（应用所有编码设置）"""
        try:
            if progress_callback:
                progress_callback("🔧 开始编码基础视频...")

            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return None

            # 创建临时编码视频文件
            encoded_video = self._create_temp_file(suffix='.mp4', prefix='encoded_base_')

            # 获取编码器设置
            codec, ffmpeg_params = self._get_optimal_encoder_settings(settings or {})

            # 创建临时进度文件
            progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
            with open(progress_file_path, 'w') as f:
                pass  # 创建空文件

            # 构建编码命令
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file_path,
                '-i', video_path,
                '-c:v', codec,
                '-c:a', 'aac',  # 重新编码音频确保兼容性
                '-b:a', '128k',
                '-ar', '44100',
                '-ac', '2',
                '-avoid_negative_ts', 'make_zero',
                '-fflags', '+genpts+igndts',
                '-vsync', 'cfr',  # 恒定帧率
                '-async', '1',  # 音频同步
                '-max_muxing_queue_size', '1024',
                '-movflags', '+faststart',
            ]

            # 先添加基础编码器参数
            cmd.extend(ffmpeg_params)

            # 然后添加自定义设置（如果有的话）
            if settings:
                # 添加自定义分辨率设置
                if settings.get('custom_resolution', False):
                    width = settings.get('width', 1920)
                    height = settings.get('height', 1080)
                    # 确保分辨率是偶数
                    width = width if width % 2 == 0 else width + 1
                    height = height if height % 2 == 0 else height + 1
                    cmd.extend(['-vf', f'scale={width}:{height}'])
                    if progress_callback:
                        progress_callback(f"应用自定义分辨率: {width}x{height}")

                # 添加自定义压缩设置
                if settings.get('custom_compression', False):
                    compression_bitrate = settings.get('current_compression_bitrate')

                    # 如果没有current_compression_bitrate，尝试从其他设置获取
                    if compression_bitrate is None:
                        compression_bitrate = settings.get('first_compression_bitrate', settings.get('other_compression_bitrate', 5000))
                        if progress_callback:
                            progress_callback(f"⚠️ 使用备用码率设置: {compression_bitrate}k")

                    compression_bitrate = max(1000, min(20000, compression_bitrate))
                    cmd.extend(['-b:v', f'{compression_bitrate}k'])
                    if progress_callback:
                        progress_callback(f"应用自定义码率: {compression_bitrate}k")

            if progress_callback:
                has_custom = settings and (settings.get('custom_resolution', False) or settings.get('custom_compression', False))
                if has_custom:
                    progress_callback("使用基础编码器参数 + 自定义设置")
                else:
                    progress_callback("使用默认编码器参数")

            # 添加输出文件
            cmd.append(encoded_video)

            if progress_callback:
                progress_callback("正在编码基础视频...")

            # 注册进程到管理器
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            video_ffmpeg_manager.register_process(process)

            try:
                # 导入错误日志记录器
                try:
                    from common.error_logger import error_logger
                except ImportError:
                    error_logger = None

                # 监控进度
                monitor_thread = None
                if progress_callback:
                    try:
                        video_duration = self.get_media_duration(video_path)
                        monitor_state, threads = self._monitor_ffmpeg_progress(
                            progress_file_path, video_duration, progress_callback, "基础编码", process
                        )
                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"⚠️ 无法启动进度监控: {e}")
                        monitor_state = None

                # 等待进程完成（改进版：检测进度监控状态）
                stdout, stderr = self._wait_for_process_with_monitoring(
                    process, monitor_state, progress_callback, "基础视频编码"
                )

                if process.returncode != 0:
                    # 记录错误到日志
                    if error_logger:
                        error_logger.log_ffmpeg_error("基础视频编码", cmd, stderr, process.returncode)

                    if progress_callback:
                        progress_callback(f"❌ 基础视频编码失败: {stderr}")
                    return None

                if progress_callback:
                    progress_callback("✅ 基础视频编码完成")

                return encoded_video

            finally:
                # 注销进程
                video_ffmpeg_manager.unregister_process(process)
                # 清理临时文件
                try:
                    os.unlink(progress_file_path)
                except Exception:
                    pass

        except Exception as e:
            # 记录一般错误到日志
            try:
                from common.error_logger import error_logger
                error_logger.log_general_error("编码基础视频", str(e), f"视频路径: {video_path}")
            except ImportError:
                pass

            if progress_callback:
                progress_callback(f"❌ 编码基础视频失败: {e}")
            return None

    def _loop_encoded_video_fast(self, encoded_video, target_duration, output_path, progress_callback):
        """使用编码后的视频进行快速循环"""
        try:
            if progress_callback:
                progress_callback("🔄 开始快速循环编码后的视频...")

            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return False

            # 获取编码后视频的时长
            video_duration = self.get_media_duration(encoded_video)

            # 计算需要循环的次数
            loop_count = max(1, int(target_duration / video_duration) + 1)

            if progress_callback:
                progress_callback(f"编码后视频时长: {video_duration:.2f}秒，目标: {target_duration:.2f}秒")
                progress_callback(f"需要循环 {loop_count} 次")

            # 创建临时concat文件
            concat_file_path = self._create_temp_file(suffix='.txt', prefix='concat_')

            with open(concat_file_path, 'w', encoding='utf-8') as concat_file:
                # 写入concat文件内容
                for i in range(loop_count):
                    abs_video_path = os.path.abspath(encoded_video).replace('\\', '/')
                    concat_file.write(f"file '{abs_video_path}'\n")

            # 创建临时进度文件
            progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
            with open(progress_file_path, 'w') as f:
                pass  # 创建空文件

            # 使用concat + copy模式快速拼接（不重新编码）
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file_path,
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_file_path,
                '-t', str(target_duration),  # 精确裁剪到目标时长
                '-c', 'copy',  # 直接复制，不重新编码
                '-avoid_negative_ts', 'make_zero',
                '-fflags', '+genpts+igndts',  # 重新生成时间戳并忽略DTS
                '-movflags', '+faststart',
                output_path
            ]

            # 打印完整命令到控制台
            print("=" * 80)
            print("🔧 编码后快速循环FFmpeg命令:")
            print(" ".join(cmd))
            print("=" * 80)



            print("📺 编码后快速循环FFmpeg输出开始:")
            print("-" * 80)

            # 设置启动信息（Windows下隐藏控制台窗口）
            startupinfo = None
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE

            # 启动进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                encoding='utf-8',
                errors='ignore',
                startupinfo=startupinfo
            )

            try:
                # 监控进度
                if progress_callback:
                    try:
                        total_duration = target_duration
                        self._monitor_ffmpeg_progress(
                            progress_file_path, total_duration, progress_callback, "快速循环", process
                        )
                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"⚠️ 无法启动进度监控: {e}")

                # 等待进程完成
                stdout, stderr = process.communicate()

                print("-" * 80)
                print(f"📊 编码后快速循环FFmpeg进程结束，返回码: {process.returncode}")
                print("=" * 80)

                if process.returncode != 0:
                    # 检查是否是用户手动中止
                    if self._is_user_terminated(process.returncode, stderr):
                        if progress_callback:
                            progress_callback("⏹️ 快速循环已停止")
                        return "USER_STOPPED"
                    else:
                        if progress_callback:
                            progress_callback(f"❌ 快速循环失败: {stderr}")
                        return False

                if progress_callback:
                    progress_callback("✅ 快速循环完成")

                return True

            except Exception as e:
                print(f"💥 编码后快速循环FFmpeg执行出错: {e}")
                if progress_callback:
                    progress_callback(f"❌ 快速循环失败: {e}")
                return False
            finally:
                # 清理临时进度文件
                try:
                    os.unlink(progress_file_path)
                except Exception:
                    pass

        except Exception as e:
            if progress_callback:
                progress_callback(f"快速循环失败: {e}")
            return False
        finally:
            # 清理concat文件
            try:
                os.unlink(concat_file_path)
            except Exception:
                pass

    def _loop_video_with_encoding_legacy(self, video_path, target_duration, output_path, loop_count, settings, progress_callback):
        """传统的循环编码方式（回退方案）"""
        ffmpeg_exe = self._get_ffmpeg_executable()

        # 获取原视频时长
        video_duration = self.get_media_duration(video_path)

        # 创建临时concat文件
        concat_file_path = self._create_temp_file(suffix='.txt', prefix='concat_')

        with open(concat_file_path, 'w', encoding='utf-8') as concat_file:
            # 写入concat文件内容
            for i in range(loop_count):
                abs_video_path = os.path.abspath(video_path).replace('\\', '/')
                concat_file.write(f"file '{abs_video_path}'\n")

            # 获取编码器设置
            codec, ffmpeg_params = self._get_optimal_encoder_settings(settings or {})

            # 创建临时进度文件
            progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
            with open(progress_file_path, 'w') as f:
                pass  # 创建空文件

            # 使用concat + 重新编码，添加完整的时间戳和同步参数
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file_path,
                '-f', 'concat',
                '-safe', '0',
                '-i', concat_file_path,
                '-c:v', codec,
                '-c:a', 'aac',  # 重新编码音频确保兼容性
                '-b:a', '128k',
                '-ar', '44100',
                '-ac', '2',
                '-avoid_negative_ts', 'make_zero',  # 修复负时间戳
                '-fflags', '+genpts+igndts',  # 重新生成时间戳并忽略DTS
                '-vsync', 'cfr',  # 恒定帧率
                '-async', '1',  # 音频同步
                '-max_muxing_queue_size', '1024',  # 增加缓冲区
                '-movflags', '+faststart',  # 优化播放
            ]

            # 精确计算目标时长
            total_loop_duration = video_duration * loop_count
            if target_duration < total_loop_duration - 0.1:  # 留0.1秒容差
                cmd.extend(['-t', f"{target_duration:.3f}"])  # 使用更精确的时长
                if progress_callback:
                    progress_callback(f"重新编码并精确裁剪到: {target_duration:.3f}秒")
            else:
                if progress_callback:
                    progress_callback(f"重新编码保持完整时长: {total_loop_duration:.3f}秒")

            # 应用编码设置
            if settings and settings.get('custom_resolution', False):
                width = settings.get('width', 1920)
                height = settings.get('height', 1080)
                cmd.extend(['-vf', f'scale={width}:{height}'])
                if progress_callback:
                    progress_callback(f"应用自定义分辨率: {width}x{height}")
            else:
                if progress_callback:
                    progress_callback("保持原始分辨率")

            # 添加压缩设置
            if settings and settings.get('custom_compression', False):
                compression_bitrate = settings.get('current_compression_bitrate')

                # 如果没有current_compression_bitrate，尝试从其他设置获取
                if compression_bitrate is None:
                    compression_bitrate = settings.get('first_compression_bitrate', settings.get('other_compression_bitrate', 5000))

                cmd.extend(['-b:v', f'{compression_bitrate}k'])
                if progress_callback:
                    progress_callback(f"应用码率设置: {compression_bitrate}k")
            else:
                cmd.extend(['-crf', '28'])

            # 添加编码器优化参数
            cmd.extend(ffmpeg_params)
            cmd.append(output_path)

            if progress_callback:
                progress_callback("正在传统循环编码...")

            # 注册进程到管理器
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            video_ffmpeg_manager.register_process(process)

            try:
                # 监控进度
                if progress_callback:
                    try:
                        total_duration = video_duration * loop_count
                        self._monitor_ffmpeg_progress(progress_file_path, total_duration, progress_callback, "传统循环编码")
                    except Exception:
                        pass

                # 等待进程完成
                stdout, stderr = process.communicate()

                if process.returncode != 0:
                    if self._is_user_terminated(process.returncode, stderr):
                        if progress_callback:
                            progress_callback("传统循环编码已停止")
                        return "USER_STOPPED"
                    else:
                        raise Exception(f"传统循环编码失败: {stderr}")

                if progress_callback:
                    progress_callback("传统循环编码完成")

                return True

            except Exception as e:
                if progress_callback:
                    progress_callback(f"传统循环编码失败: {e}")
                return False
            finally:
                # 注销进程
                video_ffmpeg_manager.unregister_process(process)
                # 清理临时文件
                try:
                    os.unlink(concat_file_path)
                    os.unlink(progress_file_path)
                except Exception:
                    pass





    def merge_audio_video(self, video_path, audio_path, output_path, settings=None, progress_callback=None):
        """合并音频和视频，支持BGM混音"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        # 检查是否启用BGM
        enable_bgm = settings.get('enable_bgm', False) if settings else False

        if enable_bgm:
            if progress_callback:
                progress_callback("正在合并音视频并添加BGM...")
            return self._merge_audio_video_with_bgm(video_path, audio_path, output_path, settings, progress_callback)
        else:
            if progress_callback:
                progress_callback("正在合并音视频...")
            return self._merge_audio_video_simple(video_path, audio_path, output_path, settings, progress_callback)

    def _merge_audio_video_simple(self, video_path, audio_path, output_path, settings=None, progress_callback=None):
        """简单的音视频合并（无BGM）"""
        ffmpeg_exe = self._get_ffmpeg_executable()

        # 获取音视频时长，使用较短的作为输出时长
        try:
            video_duration = self.get_media_duration(video_path)
            audio_duration = self.get_media_duration(audio_path)
            min_duration = min(video_duration, audio_duration)
        except Exception:
            min_duration = None

        # 获取并行处理设置
        enable_parallel = settings.get('enable_parallel', True) if settings else True
        max_threads = settings.get('max_threads', 4) if settings else 4

        # 检测音频格式，优化编码策略
        audio_codec = self._detect_audio_codec(audio_path)

        # 创建临时进度文件
        progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
        with open(progress_file_path, 'w') as f:
            pass  # 创建空文件

        # 获取音视频时长，确保同步
        video_duration = self.get_media_duration(video_path)
        audio_duration = self.get_media_duration(audio_path)

        if progress_callback:
            progress_callback(f"视频时长: {video_duration:.2f}秒，音频时长: {audio_duration:.2f}秒")

        # 使用较短的时长作为输出时长，避免不同步
        target_duration = min(video_duration, audio_duration)

        # 获取音频合并专用编码器设置
        audio_merge_settings = settings.copy() if settings else {}
        audio_merge_encoder = audio_merge_settings.get('audio_merge_encoder', 'auto')
        if audio_merge_encoder != 'auto':
            audio_merge_settings['encoder'] = audio_merge_encoder

        # 获取编码器设置
        video_codec, video_params = self._get_optimal_encoder_settings(audio_merge_settings)

        if progress_callback:
            encoder_type = "NVENC硬件编码器" if 'nvenc' in video_codec else "软件编码器"
            progress_callback(f"正在合并音频... (使用{encoder_type})")

        cmd = [
            ffmpeg_exe, '-y',
            '-progress', progress_file_path,  # 输出进度到文件
            '-i', video_path,
            '-i', audio_path,
            '-t', str(target_duration),  # 明确指定输出时长
            '-c:v', video_codec,  # 使用指定的视频编码器
            '-c:a', 'aac',   # 重新编码音频确保兼容性
            '-b:a', '128k',
            '-ar', '44100',
            '-ac', '2',
            '-map', '0:v:0',  # 明确映射视频流
            '-map', '1:a:0',  # 明确映射音频流
            '-avoid_negative_ts', 'make_zero',
            '-fflags', '+genpts',
            '-movflags', '+faststart',  # 优化播放兼容性和跳转
            # 添加关键帧设置，改善跳转性能
            '-g', '30',  # 关键帧间隔1秒（30fps）
            '-keyint_min', '15',  # 最小关键帧间隔
            '-sc_threshold', '40'  # 场景切换阈值
        ]

        # 添加视频编码参数
        cmd.extend(video_params)

        # 应用自定义码率（确保合并音频阶段也受控）
        if settings and settings.get('custom_compression', False):
            compression_bitrate = settings.get('current_compression_bitrate')
            if compression_bitrate is None:
                compression_bitrate = settings.get('first_compression_bitrate', settings.get('other_compression_bitrate', 5000))
            try:
                compression_bitrate = max(1000, min(20000, int(compression_bitrate)))
            except Exception:
                compression_bitrate = 5000
            cmd.extend(['-b:v', f'{compression_bitrate}k'])
            # 为常见编码器补充码率兼容参数
            if 'nvenc' in video_codec:
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{int(compression_bitrate * 2)}k",
                ])
            elif video_codec == 'libx264':
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{int(compression_bitrate * 2)}k",
                ])

        # 智能音频处理：如果已经是AAC就直接复制，否则快速编码
        if audio_codec and audio_codec.lower() in ['aac', 'mp4a']:
            cmd.extend(['-c:a', 'copy'])  # 直接复制AAC音频，最快
            if progress_callback:
                progress_callback("音频已是AAC格式，直接复制...")
        else:
            cmd.extend(['-c:a', 'aac'])   # 重新编码为AAC
            # 添加快速编码参数
            cmd.extend(['-aac_coder', 'fast'])  # 使用快速AAC编码器

            # 添加线程参数（仅用于音频编码）
            if enable_parallel and max_threads > 0:
                cmd.extend(['-threads', str(max_threads)])

            if progress_callback:
                progress_callback(f"音频格式为{audio_codec}")

        # 如果有明确的时长限制，添加-t参数
        if min_duration:
            cmd.extend(['-t', str(min_duration)])

        cmd.append(output_path)

        try:
            # 启动FFmpeg进程（隐藏窗口）
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback and min_duration:
                self._monitor_ffmpeg_progress(progress_file_path, min_duration, progress_callback, "合并音视频")

            # 等待进程完成
            stdout, stderr = process.communicate()



            if process.returncode != 0:
                # 检查是否是用户手动中止
                if self._is_user_terminated(process.returncode, stderr):
                    if progress_callback:
                        progress_callback("音视频合并已停止")
                    return "USER_STOPPED"  # 返回特殊值表示用户中止
                else:
                    # 只有在不是用户中止的情况下才报告错误
                    raise Exception(f"音视频合并失败: {stderr}")

            if progress_callback:
                progress_callback("音视频合并完成")

            return True

        finally:
            # 注销进程
            if 'process' in locals():
                video_ffmpeg_manager.unregister_process(process)

            # 清理临时进度文件
            try:
                os.unlink(progress_file_path)
            except Exception:
                pass

    def _merge_audio_video_with_bgm(self, video_path, audio_path, output_path, settings, progress_callback=None):
        """音视频合并并添加BGM混音"""
        ffmpeg_exe = self._get_ffmpeg_executable()

        # 获取BGM设置
        bgm_path = settings.get('bgm_path', '')
        bgm_mode = settings.get('bgm_mode', '单文件循环')
        bgm_volume = settings.get('bgm_volume', 15)  # BGM音量百分比

        if not bgm_path or not os.path.exists(bgm_path):
            raise Exception("BGM路径无效或文件不存在")

        # 选择BGM文件
        selected_bgm = self._select_bgm_file(bgm_path, bgm_mode, progress_callback)
        if not selected_bgm:
            raise Exception("无法选择BGM文件")

        # 获取时长信息
        video_duration = self.get_media_duration(video_path)
        audio_duration = self.get_media_duration(audio_path)
        target_duration = min(video_duration, audio_duration)

        if progress_callback:
            progress_callback(f"视频时长: {video_duration:.2f}秒，音频时长: {audio_duration:.2f}秒")
            progress_callback(f"BGM文件: {os.path.basename(selected_bgm)}")
            progress_callback(f"BGM音量: {bgm_volume}%")

        # 创建临时进度文件
        progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
        with open(progress_file_path, 'w') as f:
            pass  # 创建空文件

        # 计算BGM音量（转换为FFmpeg的音量值）
        bgm_volume_db = self._calculate_bgm_volume(bgm_volume)

        # 获取音频合并专用编码器设置
        audio_merge_settings = settings.copy() if settings else {}
        audio_merge_encoder = audio_merge_settings.get('audio_merge_encoder', 'auto')
        if audio_merge_encoder != 'auto':
            audio_merge_settings['encoder'] = audio_merge_encoder

        # 获取编码器设置
        video_codec, video_params = self._get_optimal_encoder_settings(audio_merge_settings)

        # 使用更简单和稳定的方法：总是忽略视频中的音频，只使用外部音频和BGM
        if progress_callback:
            encoder_type = "NVENC硬件编码器" if 'nvenc' in video_codec else "软件编码器"
            progress_callback(f"正在混合主音频和BGM... (使用{encoder_type})")

        # 构建优化的FFmpeg命令：忽略视频音频，使用外部音频+BGM混音
        speed_priority = settings.get('speed_priority', True)

        cmd = [
            ffmpeg_exe, '-y',
            '-progress', progress_file_path,
            '-i', video_path,      # 输入0: 视频（忽略其音频）
            '-i', audio_path,      # 输入1: 主音频
            '-i', selected_bgm,    # 输入2: BGM
            '-filter_complex',
            f'[1:a]volume=1.0[main_audio];'  # 主音频保持原音量
            f'[2:a]volume={bgm_volume_db},aloop=-1:2e+09[bgm_loop];'  # BGM循环并调整音量
            f'[main_audio][bgm_loop]amix=inputs=2:duration=shortest:normalize=0[mixed_audio]',  # 混音（禁用标准化提升速度）
            '-map', '0:v',         # 映射视频流（只要视频，不要音频）
            '-map', '[mixed_audio]',  # 映射混音后的音频
            '-c:v', video_codec,   # 使用指定的视频编码器
            '-c:a', 'aac',         # 音频编码为AAC
            '-b:a', '128k',
            '-profile:a', 'aac_low',  # 优化音频配置
            '-ar', '44100',
            '-ac', '2',
            '-t', str(target_duration),  # 限制输出时长
        ]

        # 根据速度优先模式添加不同的优化参数
        if speed_priority:
            cmd.extend([
                '-avoid_negative_ts', 'make_zero',
                '-fflags', '+genpts+flush_packets',
                '-max_muxing_queue_size', '4096',
                '-thread_queue_size', '2048',
                '-flush_packets', '1',
                '-copyts',
                '-start_at_zero',
                '-movflags', '+faststart',  # 优化播放兼容性和跳转
                # 添加关键帧设置，改善跳转性能
                '-g', '30',  # 关键帧间隔1秒（30fps）
                '-keyint_min', '15',  # 最小关键帧间隔
                '-sc_threshold', '40'  # 场景切换阈值
            ])
        else:
            cmd.extend([
                '-avoid_negative_ts', 'make_zero',
                '-fflags', '+genpts',
                '-max_muxing_queue_size', '2048',
                '-movflags', '+faststart',  # 优化播放兼容性和跳转
                # 添加关键帧设置，改善跳转性能
                '-g', '30',  # 关键帧间隔1秒（30fps）
                '-keyint_min', '15',  # 最小关键帧间隔
                '-sc_threshold', '40'  # 场景切换阈值
            ])

        # 添加视频编码参数
        cmd.extend(video_params)

        # 应用自定义码率（确保BGM混音阶段也受控）
        if settings and settings.get('custom_compression', False):
            compression_bitrate = settings.get('current_compression_bitrate')
            if compression_bitrate is None:
                compression_bitrate = settings.get('first_compression_bitrate', settings.get('other_compression_bitrate', 5000))
            try:
                compression_bitrate = max(1000, min(20000, int(compression_bitrate)))
            except Exception:
                compression_bitrate = 5000
            cmd.extend(['-b:v', f'{compression_bitrate}k'])
            if 'nvenc' in video_codec:
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{int(compression_bitrate * 2)}k",
                ])
            elif video_codec == 'libx264':
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{int(compression_bitrate * 2)}k",
                ])
        cmd.append(output_path)

        try:
            # 启动FFmpeg进程
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback:
                self._monitor_ffmpeg_progress(progress_file_path, target_duration, progress_callback, "BGM混音")

            # 等待进程完成
            stdout, stderr = process.communicate()

            # 保存调试信息
            try:
                output_dir = os.path.dirname(output_path)
                debug_cmd_file = os.path.join(output_dir, "ffmpeg_bgm_command.txt")
                with open(debug_cmd_file, 'w', encoding='utf-8') as f:
                    f.write(f"BGM混音命令:\n{' '.join(cmd)}\n\n")
                    f.write(f"返回码: {process.returncode}\n\n")
                    f.write(f"视频文件: {video_path}\n")
                    f.write(f"音频文件: {audio_path}\n")
                    f.write(f"BGM文件: {selected_bgm}\n")
                    f.write(f"BGM音量: {bgm_volume}% ({bgm_volume_db})\n")
                    f.write(f"目标时长: {target_duration:.2f}秒\n\n")
                    f.write(f"标准输出:\n{stdout}\n\n")
                    f.write(f"错误输出:\n{stderr}\n")
            except:
                pass

            if process.returncode != 0:
                # 检查是否是用户手动中止
                if self._is_user_terminated(process.returncode, stderr):
                    if progress_callback:
                        progress_callback("BGM混音已停止")
                    return "USER_STOPPED"  # 返回特殊值表示用户中止
                else:
                    # 只有在不是用户中止的情况下才报告错误
                    error_msg = f"BGM混音失败: {stderr}"
                    if "Invalid data found when processing input" in stderr:
                        error_msg += "\n提示: 可能是音频文件格式不兼容，请尝试使用MP3格式的BGM文件"
                    elif "No such filter" in stderr:
                        error_msg += "\n提示: FFmpeg版本可能不支持某些音频滤镜，请更新FFmpeg"
                    elif "does not contain any stream" in stderr:
                        error_msg += "\n提示: 某个输入文件可能损坏或格式不正确"

                    raise Exception(error_msg)

            if progress_callback:
                progress_callback("BGM混音完成")

            return True

        finally:
            # 注销进程
            if 'process' in locals():
                video_ffmpeg_manager.unregister_process(process)

            # 清理临时进度文件
            try:
                os.unlink(progress_file_path)
            except Exception:
                pass

    def _select_bgm_file(self, bgm_path, bgm_mode, progress_callback=None):
        """根据BGM模式选择BGM文件"""
        if bgm_mode == "单文件循环":
            # 单文件模式，直接返回文件路径
            if os.path.isfile(bgm_path):
                return bgm_path
            else:
                raise Exception("BGM文件不存在")

        elif bgm_mode == "文件夹随机":
            # 文件夹随机模式，随机选择一个音频文件
            if not os.path.isdir(bgm_path):
                raise Exception("BGM路径不是有效的文件夹")

            # 支持的音频格式
            audio_extensions = {'.mp3', '.wav', '.aac', '.m4a', '.flac', '.ogg', '.wma'}

            # 获取文件夹中的所有音频文件
            audio_files = []
            for file in os.listdir(bgm_path):
                if os.path.splitext(file.lower())[1] in audio_extensions:
                    audio_files.append(os.path.join(bgm_path, file))

            if not audio_files:
                raise Exception("BGM文件夹中没有找到音频文件")

            # 随机选择一个文件
            import random
            selected_file = random.choice(audio_files)

            if progress_callback:
                progress_callback(f"随机选择BGM: {os.path.basename(selected_file)}")

            return selected_file

        else:
            raise Exception(f"不支持的BGM模式: {bgm_mode}")

    def _calculate_bgm_volume(self, volume_percent):
        """计算BGM音量值（转换为FFmpeg的volume滤镜值）"""
        # volume_percent 是0-100的百分比
        # FFmpeg的volume滤镜：1.0 = 100%，0.5 = 50%，2.0 = 200%
        return volume_percent / 100.0

    def _detect_encoder_failure_type(self, stderr):
        """检测编码器失败的类型"""
        if not stderr:
            return None

        stderr_lower = stderr.lower()

        # 检测硬件编码器失败
        hardware_failure_keywords = [
            'nvenc', 'h264_nvenc', 'hevc_nvenc',
            'qsv', 'h264_qsv', 'hevc_qsv',
            'amf', 'h264_amf', 'hevc_amf',
            'hardware', 'gpu', 'cuda',
            'driver', 'device', 'capability'
        ]

        for keyword in hardware_failure_keywords:
            if keyword in stderr_lower:
                return 'hardware_encoder_failure'

        # 检测软件编码器失败
        software_failure_keywords = [
            'libx264', 'libx265', 'software',
            'cpu', 'memory', 'out of memory'
        ]

        for keyword in software_failure_keywords:
            if keyword in stderr_lower:
                return 'software_encoder_failure'

        # 检测其他严重错误
        critical_failure_keywords = [
            'segmentation fault', 'access violation',
            'fatal error', 'critical error',
            'cannot allocate', 'insufficient memory'
        ]

        for keyword in critical_failure_keywords:
            if keyword in stderr_lower:
                return 'critical_failure'

        return 'unknown_failure'

    def _is_user_terminated(self, return_code, stderr):
        """检测是否是用户手动中止"""
        # 首先检查全局停止标志
        if hasattr(self, 'should_stop') and self.should_stop:
            return True

        # 检查进程管理器的停止状态
        try:
            from .core import video_ffmpeg_manager
            if hasattr(video_ffmpeg_manager, 'should_stop') and video_ffmpeg_manager.should_stop:
                return True
        except:
            pass

        # 检查进程管理器的用户停止时间戳
        # 这是一个更可靠的检测方法
        try:
            from .core import video_ffmpeg_manager
            import time
            current_time = time.time()
            if hasattr(video_ffmpeg_manager, '_user_stop_time'):
                # 如果在最近10秒内有用户停止请求，认为是用户中止
                if current_time - video_ffmpeg_manager._user_stop_time < 10:
                    return True
        except:
            pass

        # Windows下进程被终止的返回码通常是1或-1073741510
        # Linux下通常是-15 (SIGTERM) 或 -9 (SIGKILL)
        termination_codes = [1, -1, -15, -9, -1073741510, 1073741510]

        # 检查返回码
        if return_code in termination_codes:
            # 首先检查是否包含明确的错误信息（不是终止）
            error_keywords = [
                "Invalid data found", "No such file", "Permission denied",
                "Cannot open", "Failed to", "Error opening", "Invalid",
                "not found", "No such filter", "does not contain any stream",
                "Conversion failed", "could not find codec"
            ]

            # 如果包含明确的错误信息，不认为是用户终止
            if any(keyword in stderr for keyword in error_keywords):
                return False

            # 进一步检查stderr中的终止信息
            termination_keywords = [
                "Terminated", "terminated", "killed", "Killed",
                "Exiting normally", "received signal", "中断", "终止",
                "Interrupted", "interrupted"
            ]

            if any(keyword in stderr for keyword in termination_keywords):
                return True

            # 如果stderr只包含版本信息，很可能是被终止
            if "ffmpeg version" in stderr and len(stderr.strip()) < 1000:
                return True

            # 如果stderr为空或很短，也可能是被终止
            if len(stderr.strip()) < 50:
                return True

        return False





    def resize_video(self, video_path, resolution, output_path, progress_callback=None):
        """调整视频分辨率"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        if progress_callback:
            progress_callback(f"正在调整分辨率到 {resolution}...")

        # 解析分辨率
        if 'x' in resolution:
            width, height = resolution.split('x')
        else:
            raise Exception(f"无效的分辨率格式: {resolution}")

        # 创建临时进度文件
        progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
        with open(progress_file_path, 'w') as f:
            pass  # 创建空文件

        # 获取输入视频时长用于进度计算
        try:
            video_duration = self.get_media_duration(video_path)
        except Exception:
            video_duration = None

        cmd = [
            ffmpeg_exe, '-y',
            '-progress', progress_file_path,  # 输出进度到文件
            '-i', video_path,
            '-vf', f'scale={width}:{height}',
            '-c:a', 'copy',  # 复制音频流
            output_path
        ]

        try:
            # 启动FFmpeg进程（隐藏窗口）
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback and video_duration:
                self._monitor_ffmpeg_progress(progress_file_path, video_duration, progress_callback, "分辨率调整")

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                # 检查是否是用户手动中止
                if self._is_user_terminated(process.returncode, stderr):
                    if progress_callback:
                        progress_callback("分辨率调整已停止")
                    return "USER_STOPPED"  # 返回特殊值表示用户中止
                else:
                    # 只有在不是用户中止的情况下才报告错误
                    raise Exception(f"分辨率调整失败: {stderr}")

            if progress_callback:
                progress_callback("分辨率调整完成")

            return True

        finally:
            # 注销进程
            if 'process' in locals():
                video_ffmpeg_manager.unregister_process(process)

            # 清理临时进度文件
            try:
                import os
                os.unlink(progress_file_path)
            except Exception:
                pass

    def add_subtitles_ffmpeg(self, video_path, subtitle_path, output_path, settings=None, progress_callback=None, _is_retry=False):
        """使用FFmpeg添加字幕"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        # 字幕添加时使用专门的字幕编码器设置
        subtitle_settings = settings.copy() if settings else {}

        # 获取字幕专用编码器设置
        subtitle_encoder = subtitle_settings.get('subtitle_encoder', 'auto')
        if subtitle_encoder != 'auto':
            subtitle_settings['encoder'] = subtitle_encoder

        # 获取编码器设置
        codec, ffmpeg_params = self._get_optimal_encoder_settings(subtitle_settings)

        # 检查是否是硬件编码器（仅NVENC）
        is_hardware_encoder = 'nvenc' in codec
        use_hardware_for_subtitle = subtitle_settings.get('use_hardware_acceleration', True)

        if is_hardware_encoder and use_hardware_for_subtitle:
            if progress_callback:
                progress_callback("正在添加字幕... (使用硬件加速)")
        else:
            if progress_callback:
                encoder_type = "软件编码器" if codec == 'libx264' else "硬件编码器"
                progress_callback(f"正在添加字幕... (使用{encoder_type})")


        ass_subtitle_path = self._convert_srt_to_ass(subtitle_path, settings, progress_callback)
        if not ass_subtitle_path:
            raise Exception("字幕格式转换失败")


        try:
            # 创建安全的字幕文件路径（避免特殊字符和路径问题）
            import shutil
            temp_dir = self._get_temp_dir()
            if not temp_dir:
                import tempfile
                temp_dir = tempfile.mkdtemp()
            else:
                import uuid
                temp_dir = os.path.join(temp_dir, f"subtitle_{uuid.uuid4().hex[:8]}")
                os.makedirs(temp_dir, exist_ok=True)

            safe_subtitle_path = os.path.join(temp_dir, "subtitle.ass")
            shutil.copy2(ass_subtitle_path, safe_subtitle_path)

            # 获取视频时长用于进度计算
            try:
                video_duration = self.get_media_duration(video_path)
            except Exception:
                video_duration = None

            # 获取并行处理设置
            enable_parallel = settings.get('enable_parallel', True) if settings else True
            max_threads = settings.get('max_threads', 4) if settings else 4

            # 创建临时进度文件
            progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
            with open(progress_file_path, 'w') as f:
                pass  # 创建空文件

            # 使用用户设置的编码器，而不是写死
            subtitle_codec = codec
            subtitle_params = ffmpeg_params.copy()

            # 为软件编码器添加优化参数
            if codec == 'libx264':
                subtitle_params.extend([
                    '-tune', 'zerolatency',  # 零延迟
                    '-x264opts', 'no-scenecut:no-mbtree:no-mixed-refs'  # 禁用耗时特性
                ])

            # 字幕处理优化：根据编码器类型显示相应的优化信息
            # 注意：字幕渲染(subtitles滤镜)本身不支持多线程，但编码器可以使用多线程
            # 只为软件编码器添加线程参数，硬件编码器不需要
            if enable_parallel and max_threads > 0 and codec == 'libx264':
                subtitle_params.extend(['-threads', str(max_threads)])

            # 构建优化的字幕添加命令
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file_path,  # 输出进度到文件
                '-i', video_path,
                '-i', safe_subtitle_path,  # 直接作为输入文件
            ]

            # 使用标准字幕渲染方式
            subtitle_filter_path = safe_subtitle_path.replace('\\', '/').replace(':', '\\:')

            # 标准字幕渲染：适用于所有编码器
            cmd.extend([
                '-vf', f'subtitles=filename=\'{subtitle_filter_path}\':force_style=\'Fontsize=24,Bold=1\'',
                '-c:v', subtitle_codec
            ])

            # 添加音频和编码参数
            cmd.extend(['-c:a', 'copy'])
            cmd.extend(subtitle_params)

            # 添加性能优化参数
            cmd.extend([
                '-movflags', '+faststart',  # 快速启动
                '-max_muxing_queue_size', '2048',  # 增加缓冲区
                '-fflags', '+genpts',  # 重新生成时间戳
                '-avoid_negative_ts', 'make_zero',  # 避免负时间戳
                '-vsync', 'cfr'  # 恒定帧率，提高处理速度
            ])

            cmd.append(output_path)

            try:
                # 启动FFmpeg进程（隐藏窗口）
                import platform
                if platform.system() == "Windows":
                    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                             text=True, encoding='utf-8', errors='ignore',
                                             creationflags=subprocess.CREATE_NO_WINDOW)
                else:
                    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                             text=True, encoding='utf-8', errors='ignore')

                # 监控进度（只在非重试时显示详细进度）
                if progress_callback and video_duration and not _is_retry:
                    self._monitor_ffmpeg_progress(progress_file_path, video_duration, progress_callback, "添加字幕")
                elif _is_retry and progress_callback:
                    # 重试时只显示简单状态，不显示百分比进度
                    progress_callback("正在重试字幕添加...")

                # 等待进程完成
                stdout, stderr = process.communicate()

                if process.returncode != 0:
                    # 检查是否是硬件编码器失败
                    is_hardware_encoder = 'nvenc' in codec

                    if is_hardware_encoder and use_hardware_for_subtitle:
                        # 硬件编码器失败，尝试软件编码器
                        if progress_callback:
                            progress_callback("硬件编码失败，切换到软件编码...")

                        # 清理当前的临时文件
                        try:
                            os.unlink(progress_file_path)
                        except Exception:
                            pass

                        # 使用软件编码器重试（标记为重试，避免重复进度显示）
                        return self._add_subtitles_with_software_encoder(video_path, safe_subtitle_path, output_path, settings, progress_callback, _is_retry=True)
                    else:
                        # 软件编码器也失败，使用最简单的备用方案
                        if progress_callback:
                            progress_callback("字幕添加失败，尝试最简备用方案...")

                        # 清理当前的临时文件
                        try:
                            os.unlink(progress_file_path)
                        except Exception:
                            pass

                        # 尝试备用方案（标记为重试，避免重复进度显示）
                        return self._add_subtitles_fallback(video_path, safe_subtitle_path, output_path, progress_callback, _is_retry=True)

                if progress_callback:
                    progress_callback("字幕添加完成")

            finally:
                # 清理临时进度文件
                try:
                    os.unlink(progress_file_path)
                except Exception:
                    pass

            return True

        finally:
            # 清理临时ASS文件
            if ass_subtitle_path and os.path.exists(ass_subtitle_path):
                try:
                    os.unlink(ass_subtitle_path)
                except Exception:
                    pass

            # 清理安全路径临时文件
            if 'safe_subtitle_path' in locals():
                try:
                    if os.path.exists(safe_subtitle_path):
                        os.unlink(safe_subtitle_path)
                except Exception:
                    pass

            # 清理临时目录
            if 'temp_dir' in locals() and os.path.exists(temp_dir):
                try:
                    import shutil
                    shutil.rmtree(temp_dir)
                except Exception:
                    pass

    def _convert_srt_to_ass(self, srt_path, settings, progress_callback=None):
        """将SRT字幕转换为ASS格式"""
        try:

            # 解析字幕样式
            subtitle_style_str = settings.get('subtitle_style', '微软雅黑 22pt 正常 白色 描边黑色2px') if settings else '微软雅黑 22pt 正常 白色 描边黑色2px'
            font_name, font_size, font_weight, font_color, outline_color, outline_width, font_spacing, position = self._parse_subtitle_style_with_position(subtitle_style_str)

            # 读取SRT内容
            with open(srt_path, 'r', encoding='utf-8') as f:
                srt_content = f.read()

            # 解析SRT内容
            subtitle_entries = self._parse_srt_content(srt_content)

            # 创建临时ASS文件
            ass_path = self._create_temp_file(suffix='.ass', prefix='subtitle_')
            with open(ass_path, 'w', encoding='utf-8') as temp_ass:

                # 写入ASS文件头
                temp_ass.write(self._generate_ass_header())

                # 写入样式定义
                temp_ass.write(self._generate_ass_style(font_name, font_size, font_weight, font_color, outline_color, outline_width, font_spacing, position))

                # 写入事件部分
                temp_ass.write("\n[Events]\n")
                temp_ass.write("Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n")

                # 转换字幕条目
                for entry in subtitle_entries:
                    start_time = self._seconds_to_ass_time(entry['start'])
                    end_time = self._seconds_to_ass_time(entry['end'])
                    text = entry['text'].replace('\n', '\\N')  # ASS换行符

                    # 写入字幕事件
                    temp_ass.write(f"Dialogue: 0,{start_time},{end_time},Default,,0,0,0,,{text}\n")



            return ass_path

        except Exception as e:
            print(f"SRT转ASS失败: {e}")
            return None

    def _generate_ass_header(self):
        """生成ASS文件头"""
        return """[Script Info]
Title: Generated by AI Voice Synthesis
ScriptType: v4.00+

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
"""

    def _generate_ass_style(self, font_name, font_size, font_weight, font_color, outline_color, outline_width, font_spacing, position):
        """生成ASS样式定义"""
        # 颜色转换为ASS格式（BGR格式，十六进制）
        color_map = {
            'white': '&HFFFFFF&', 'black': '&H000000&', 'red': '&H0000FF&',
            'green': '&H00FF00&', 'blue': '&HFF0000&', 'yellow': '&H00FFFF&',
            'cyan': '&HFFFF00&', 'magenta': '&HFF00FF&'
        }

        primary_color = color_map.get(font_color.lower(), '&HFFFFFF&')
        outline_color_hex = color_map.get(outline_color.lower(), '&H000000&')

        # 字体粗细
        bold = 1 if font_weight == "粗体" else 0

        # 对齐方式
        alignment_map = {
            "底部居中": "2", "顶部居中": "8", "左下角": "1", "右下角": "3",
            "左上角": "7", "右上角": "9", "居中": "5"
        }
        alignment = alignment_map.get(position, "2")

        # 边距设置
        margin_v = "20" if position in ["底部居中", "顶部居中"] else "20"
        margin_l = "20" if position in ["左下角", "左上角"] else "0"
        margin_r = "20" if position in ["右下角", "右上角"] else "0"

        # 处理字体间距，确保为数值
        try:
            spacing = int(font_spacing) if font_spacing else 0
        except (ValueError, TypeError):
            spacing = 0

        # 生成样式行，包含字体间距
        style_line = f"Style: Default,{font_name},{font_size},{primary_color},&HFFFFFF&,{outline_color_hex},&H80000000&,{bold},0,0,0,100,100,{spacing},0,1,{outline_width},2,{alignment},{margin_l},{margin_r},{margin_v},1\n"

        return style_line

    def _seconds_to_ass_time(self, seconds):
        """将秒数转换为ASS时间格式 (H:MM:SS.CC)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        centiseconds = int((seconds % 1) * 100)

        return f"{hours}:{minutes:02d}:{secs:02d}.{centiseconds:02d}"

    def _parse_subtitle_style_with_position(self, style_str):
        """解析字幕样式字符串，包括位置信息"""
        try:
            # 先使用原有方法解析基本样式
            font_name, font_size, font_weight, font_color, outline_color, outline_width, font_spacing = self._parse_subtitle_style(style_str)

            # 解析位置信息
            position = "底部居中"  # 默认位置
            position_keywords = ["底部居中", "顶部居中", "左下角", "右下角", "左上角", "右上角", "居中"]

            for keyword in position_keywords:
                if keyword in style_str:
                    position = keyword
                    break

            return font_name, font_size, font_weight, font_color, outline_color, outline_width, font_spacing, position

        except Exception as e:
            print(f"解析字幕样式失败: {e}")
            # 返回默认值
            return "微软雅黑", 22, "normal", "white", "black", 2, 0, "底部居中"

    def _parse_subtitle_style(self, style_str):
        """解析字幕样式字符串"""
        try:
            # 默认值
            font_name = "微软雅黑"
            font_size = 22
            font_weight = "normal"
            font_color = "white"
            outline_color = "black"
            outline_width = 2
            font_spacing = 0  # 新增字体间距默认值

            # 解析字体名称（第一个词）
            parts = style_str.split()
            if parts:
                font_name = parts[0]

            # 解析字体大小
            for part in parts:
                if 'pt' in part:
                    try:
                        font_size = int(part.replace('pt', ''))
                    except ValueError:
                        pass

            # 解析字体粗细
            if '粗体' in style_str or 'bold' in style_str.lower():
                font_weight = "粗体"

            # 解析描边（先解析描边，避免与字体颜色冲突）
            if '描边' in style_str:
                # 查找描边颜色和宽度
                import re
                outline_match = re.search(r'描边(\w+)(\d+)px', style_str)
                if outline_match:
                    outline_color_cn = outline_match.group(1)
                    outline_width = int(outline_match.group(2))

                    color_map = {
                        '黑色': 'black', '白色': 'white', '红色': 'red', '绿色': 'green',
                        '蓝色': 'blue', '黄色': 'yellow', '青色': 'cyan', '洋红': 'magenta'
                    }
                    outline_color = color_map.get(outline_color_cn, 'black')

            # 解析字体颜色（排除描边部分）
            # 先移除描边部分，避免颜色解析冲突
            style_without_outline = re.sub(r'描边\w+\d+px', '', style_str) if '描边' in style_str else style_str

            colors = ['白色', '黑色', '红色', '绿色', '蓝色', '黄色', '青色', '洋红']
            for color in colors:
                if color in style_without_outline:
                    color_map = {
                        '白色': 'white', '黑色': 'black', '红色': 'red', '绿色': 'green',
                        '蓝色': 'blue', '黄色': 'yellow', '青色': 'cyan', '洋红': 'magenta'
                    }
                    font_color = color_map.get(color, 'white')
                    break

            # 解析字体间距
            if '间距' in style_str:
                import re
                spacing_match = re.search(r'间距(-?\d+)px', style_str)
                if spacing_match:
                    font_spacing = int(spacing_match.group(1))

            return font_name, font_size, font_weight, font_color, outline_color, outline_width, font_spacing

        except Exception as e:
            print(f"解析字幕样式失败: {e}")
            return "微软雅黑", 22, "normal", "white", "black", 2, 0

    def _parse_srt_content(self, srt_content):
        """解析SRT字幕内容"""
        entries = []
        blocks = srt_content.strip().split('\n\n')

        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                try:
                    # 解析时间码
                    time_line = lines[1]
                    start_str, end_str = time_line.split(' --> ')

                    start_time = self._parse_srt_time(start_str)
                    end_time = self._parse_srt_time(end_str)

                    # 合并文本行
                    text = '\n'.join(lines[2:])

                    entries.append({
                        'start': start_time,
                        'end': end_time,
                        'text': text
                    })
                except Exception as e:
                    print(f"解析SRT块失败: {e}")
                    continue

        return entries

    def _parse_srt_time(self, time_str):
        """解析SRT时间格式为秒数"""
        try:
            # SRT格式: HH:MM:SS,mmm
            time_str = time_str.replace(',', '.')
            parts = time_str.split(':')
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds = float(parts[2])

            return hours * 3600 + minutes * 60 + seconds
        except Exception:
            return 0.0

    def compose_video_with_loop(self, loop_video_path, audio_path, output_path,
                               subtitle_path=None, settings=None, progress_callback=None, pregenerated_loop_videos=None):
        """
        完整的视频合成流程：循环视频 + 音频 + 字幕

        参数:
            loop_video_path (str): 循环视频文件路径
            audio_path (str): 音频文件路径
            output_path (str): 输出视频路径
            subtitle_path (str): 字幕文件路径（可选）
            settings (dict): 合成设置
            progress_callback (function): 进度回调函数

        返回:
            bool: 是否成功
        """
        try:
            if not self.is_available():
                raise Exception("FFmpeg不可用，请检查FFmpeg安装和配置")

            self.set_progress_callback(progress_callback)

            if progress_callback:
                progress_callback("开始处理视频...")

            # 清理过期的缓存文件（静默执行）
            try:
                self.cleanup_cache()
            except:
                pass  # 忽略清理失败

            # 获取输入文件信息
            try:
                video_duration = self.get_media_duration(loop_video_path)
                audio_duration = self.get_media_duration(audio_path)

                if progress_callback:
                    progress_callback(f"原视频: {video_duration:.2f}秒，音频: {audio_duration:.2f}秒")
            except Exception as e:
                raise e

            # 检查是否需要视频编码处理
            need_video_encoding = self._need_video_encoding(settings)

            # 为循环视频设置正确的码率（使用第一集的码率作为默认）
            if settings and settings.get('custom_compression', False):
                if 'current_compression_bitrate' not in settings:
                    default_bitrate = settings.get('first_compression_bitrate', 5000)
                    settings['current_compression_bitrate'] = default_bitrate
                    if progress_callback:
                        progress_callback(f"🔧 设置循环视频码率: {default_bitrate}k (使用第一集码率)")
                else:
                    if progress_callback:
                        progress_callback(f"🔧 循环视频码率已设置: {settings['current_compression_bitrate']}k")

            # 创建临时目录和文件，使用TempFileManager跟踪
            if self.temp_manager:
                # 使用TempFileManager创建临时目录
                temp_dir = self.temp_manager.create_temp_dir(prefix="video_compose_")
            else:
                # 回退到原有逻辑
                temp_dir = self._get_temp_dir()
                if not temp_dir:
                    # 回退到系统临时目录
                    import tempfile
                    temp_dir = tempfile.mkdtemp(prefix='video_compose_')
                else:
                    # 在项目temp目录下创建子目录
                    import uuid
                    temp_dir = os.path.join(temp_dir, f"video_compose_{uuid.uuid4().hex[:8]}")
                    os.makedirs(temp_dir, exist_ok=True)

            # 创建临时文件路径
            temp_looped_video = os.path.join(temp_dir, "looped_video.mp4")
            temp_encoded_video = os.path.join(temp_dir, "encoded_video.mp4")
            temp_with_audio = os.path.join(temp_dir, "with_audio.mp4")
            temp_with_subtitle = os.path.join(temp_dir, "with_subtitle.mp4")

            # 如果使用TempFileManager，跟踪这些临时文件
            if self.temp_manager:
                # 预先注册这些文件路径，即使它们还没有被创建
                for temp_file in [temp_looped_video, temp_encoded_video, temp_with_audio, temp_with_subtitle]:
                    self.temp_manager.temp_files.add(temp_file)

            # 跟踪需要及时清理的临时文件
            temp_files_to_cleanup = []

            try:
                # 第1步：优先使用预生成的循环视频
                pregenerated_video = None
                if pregenerated_loop_videos:
                    # 查找匹配的预生成循环视频
                    for duration, video_path in pregenerated_loop_videos.items():
                        if video_path and abs(duration - audio_duration) <= 0.5:  # 0.5秒容忍误差
                            pregenerated_video = video_path
                            if progress_callback:
                                progress_callback(f"🚀 使用预生成的循环视频: {duration:.1f}秒")
                            break

                if pregenerated_video and os.path.exists(pregenerated_video):
                    # 直接复制预生成的循环视频
                    import shutil
                    shutil.copy2(pregenerated_video, temp_looped_video)
                    success = True
                    if progress_callback:
                        progress_callback(f"✅ 预生成循环视频复制完成，跳过循环生成步骤")
                else:
                    # 跳过缓存检查，每次都生成新的随机开头循环视频
                    # cached_video = self._get_cached_loop_video(loop_video_path, audio_duration, settings)

                    # if cached_video and os.path.exists(cached_video):
                    #     if progress_callback:
                    #         progress_callback("🔄 发现相同时长的循环视频，复用中...")
                    #         progress_callback(f"⚡ 使用stream_loop快速重新开始")

                    #     # 复用已有的循环视频时长，但使用原视频重新开始循环
                    #     success = self._reuse_loop_video_with_new_frame(
                    #         cached_video, loop_video_path, temp_looped_video,
                    #         settings, progress_callback
                    #     )

                    #     if not success:
                    #         if progress_callback:
                    #             progress_callback("复用失败，重新生成循环视频...")
                    #         success = self.loop_video_to_duration(loop_video_path, audio_duration, temp_looped_video, settings, progress_callback)
                    # else:
                    # 第1步：循环视频到音频时长（每次都重新生成，带随机开头）
                    if progress_callback:
                        progress_callback("第1步: 循环视频到音频时长（随机开头）...")

                    success = self.loop_video_to_duration(loop_video_path, audio_duration, temp_looped_video, settings, progress_callback)

                    # 不再缓存循环视频，确保每次都是随机开头
                    # if success and success != "USER_STOPPED":
                    #     self._cache_loop_video(loop_video_path, audio_duration, temp_looped_video, settings)
                    #     if progress_callback:
                    #         progress_callback(f"💾 已缓存循环视频，后续相同时长可复用")

                if success == "USER_STOPPED":
                    if progress_callback:
                        progress_callback("视频合成已停止")
                    return False
                elif not success or not os.path.exists(temp_looped_video):
                    raise Exception("循环视频失败")

                current_video = temp_looped_video


                # 第3步：添加字幕（如果需要）
                if subtitle_path and os.path.exists(subtitle_path) and settings and settings.get('enable_subtitle', False):
                    if progress_callback:
                        progress_callback("第3步: 添加字幕...")

                    self.add_subtitles_ffmpeg(current_video, subtitle_path, temp_with_subtitle, settings, progress_callback)

                    current_video = temp_with_subtitle

                    # 及时清理：循环视频文件已不再需要
                    try:
                        if os.path.exists(temp_looped_video):
                            os.unlink(temp_looped_video)
                            if progress_callback:
                                progress_callback("🧹 已清理循环视频临时文件")
                    except Exception:
                        pass
                else:
                    if progress_callback:
                        progress_callback("第3步: 跳过字幕添加...")

                # 第4步：合并音频
                if progress_callback:
                    progress_callback("第4步: 合并音频...")

                success = self.merge_audio_video(current_video, audio_path, temp_with_audio, settings, progress_callback)

                if success == "USER_STOPPED":
                    if progress_callback:
                        progress_callback("视频合成已停止")
                    return False
                elif not success or not os.path.exists(temp_with_audio):
                    raise Exception("合并音频失败")

                # 及时清理：字幕视频文件已不再需要
                try:
                    if current_video != temp_looped_video and os.path.exists(current_video):
                        os.unlink(current_video)
                        if progress_callback:
                            progress_callback("🧹 已清理字幕视频临时文件")
                except Exception:
                    pass

                # 第5步：输出最终视频
                if progress_callback:
                    progress_callback("第5步: 输出最终视频...")

                # 检查是否需要最终编码处理
                # 重要：如果第1步已经使用了预编码优化，则不需要再次编码
                need_final_encoding = False

                # 检查是否使用了预编码优化
                used_pre_encoding = False
                if pregenerated_video and os.path.exists(pregenerated_video):
                    # 使用了预生成的循环视频，这些视频已经编码过了
                    used_pre_encoding = True
                elif settings and self._should_use_pre_encoding(settings):
                    # 如果设置了预编码优化，说明在第1步已经编码过了
                    used_pre_encoding = True

                if used_pre_encoding:
                    # 已经预编码过了，直接复制即可
                    if progress_callback:
                        progress_callback("检测到已使用预编码优化，跳过重复编码...")
                    need_final_encoding = False
                else:
                    # 没有使用预编码，检查是否需要最终编码
                    if need_video_encoding:
                        need_final_encoding = True

                    # 检查是否在合并模式下（这种情况可能需要统一格式）
                    if settings and settings.get('enable_merge', False):
                        merge_mode = settings.get('merge_mode', 'create_merged')
                        # 只有在"合并原视频与第一集"模式下才可能需要编码
                        if merge_mode == 'merge_with_first':
                            # 这种情况下，如果没有启用视频设置，也不需要编码
                            # 因为后续的合并步骤会处理格式统一
                            pass

                if need_final_encoding:
                    # 需要编码处理
                    success = self._encode_video_with_settings(temp_with_audio, output_path, settings, progress_callback)
                else:
                    # 直接复制，不需要编码
                    success = self._copy_video(temp_with_audio, output_path, progress_callback)

                if not success:
                    raise Exception("输出最终视频失败")

                if progress_callback:
                    progress_callback("✅ 视频合成完成")

                return True

            finally:
                # 清理临时文件
                try:
                    # 如果使用TempFileManager，它会自动清理跟踪的文件
                    if self.temp_manager:
                        # TempFileManager会在cleanup_all时清理这些文件
                        pass
                    else:
                        # 回退到手动清理
                        shutil.rmtree(temp_dir)
                except Exception:
                    pass

        except Exception as e:
            if progress_callback:
                progress_callback(f"❌ 视频合成失败: {str(e)}")
            print(f"视频合成错误: {e}")
            return False

    def _need_video_encoding(self, settings):
        """检查是否需要视频编码处理"""
        if not settings:
            return False

        # 检查是否有自定义压缩设置
        if settings.get('custom_compression', False):
            return True

        # 检查是否有自定义分辨率设置
        if settings.get('custom_resolution', False):
            return True

        # 可以在这里添加其他需要编码的条件

        return False

    def _encode_video_with_settings(self, input_path, output_path, settings, progress_callback=None):
        """对视频应用所有自定义设置进行编码"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        # 使用最终输出编码器设置
        video_encoding_settings = settings.copy() if settings else {}
        final_encoder = video_encoding_settings.get('final_encoder', 'auto')
        if final_encoder != 'auto':
            video_encoding_settings['encoder'] = final_encoder

        # 获取编码器设置
        codec, ffmpeg_params = self._get_optimal_encoder_settings(video_encoding_settings)

        # 创建临时进度文件
        progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
        with open(progress_file_path, 'w') as f:
            pass  # 创建空文件

        cmd = [
            ffmpeg_exe, '-y',
            '-progress', progress_file_path,
            '-i', input_path,
            '-c:v', codec,
            '-c:a', 'copy'  # 音频直接复制
        ]

        # 添加分辨率设置
        if settings and settings.get('custom_resolution', False):
            width = settings.get('width', 1920)
            height = settings.get('height', 1080)
            cmd.extend(['-vf', f'scale={width}:{height}'])

        # 添加压缩设置（修复跳转问题）
        if settings and settings.get('custom_compression', False):
            compression_bitrate = settings.get('current_compression_bitrate', 5000)
            cmd.extend(['-b:v', f'{compression_bitrate}k'])

            # 为码率模式添加兼容性参数，修复跳转问题
            if 'nvenc' in codec:
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{compression_bitrate * 2}k",
                    '-rc', 'vbr',
                    '-g', '60',
                    '-keyint_min', '30'
                ])
            elif 'qsv' in codec:
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{compression_bitrate * 2}k",
                    '-g', '60',
                    '-keyint_min', '30'
                ])
            elif 'amf' in codec:
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{compression_bitrate * 2}k",
                    '-g', '60',
                    '-keyint_min', '30'
                ])
            elif codec == 'libx264':
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{compression_bitrate * 2}k",
                    '-g', '60',
                    '-keyint_min', '30',
                    '-profile:v', 'high',
                    '-level', '4.1'
                ])
        else:
            # 使用默认CRF
            cmd.extend(['-crf', '23'])

            # 为CRF模式也添加关键帧设置
            if 'nvenc' in codec or 'qsv' in codec or 'amf' in codec:
                cmd.extend(['-g', '60', '-keyint_min', '30'])
            elif codec == 'libx264':
                cmd.extend(['-g', '60', '-keyint_min', '30', '-profile:v', 'high', '-level', '4.1'])

        # 添加编码器优化参数
        cmd.extend(ffmpeg_params)

        # 添加大幅优化的输出格式参数
        output_format = settings.get('format', 'mp4') if settings else 'mp4'
        speed_priority = settings.get('speed_priority', True) if settings else True

        if output_format == 'mp4':
            if speed_priority:
                # 速度优先模式 - 最大化写入性能
                cmd.extend([
                    '-movflags', '+faststart+frag_keyframe+separate_moof+omit_tfhd_offset',
                    '-max_muxing_queue_size', '4096',
                    '-thread_queue_size', '2048',
                    '-flush_packets', '1',
                    '-write_tmcd', '0'  # 禁用时间码写入
                ])
            else:
                cmd.extend(['-movflags', '+faststart'])

        # 添加通用性能优化参数
        if speed_priority:
            cmd.extend([
                '-avoid_negative_ts', 'make_zero',
                '-fflags', '+genpts+flush_packets',
                '-copyts',
                '-start_at_zero'
            ])

        cmd.append(output_path)

        if progress_callback:
            encoder_type = "NVENC硬件编码器" if 'nvenc' in codec else "软件编码器"
            progress_callback(f"正在进行视频编码... (使用{encoder_type})")

        try:
            # 获取输入视频时长用于进度计算
            try:
                input_duration = self.get_media_duration(input_path)
            except Exception:
                input_duration = None

            # 启动FFmpeg进程
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback and input_duration:
                self._monitor_ffmpeg_progress(progress_file_path, input_duration, progress_callback, "视频编码")

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                raise Exception(f"视频编码失败: {stderr}")

            return True

        finally:
            # 注销进程
            video_ffmpeg_manager.unregister_process(process)

            # 清理临时进度文件
            try:
                os.unlink(progress_file_path)
            except Exception:
                pass

    def _copy_video(self, input_path, output_path, progress_callback=None):
        """直接复制视频文件"""
        try:
            if progress_callback:
                progress_callback("复制视频文件...")

            import shutil
            shutil.copy2(input_path, output_path)

            if progress_callback:
                progress_callback("视频复制完成")

            return True
        except Exception as e:
            if progress_callback:
                progress_callback(f"视频复制失败: {str(e)}")
            return False

    def _basic_encode(self, input_path, output_path, settings, progress_callback=None):
        """基本编码（无自定义设置时使用）"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        # 创建临时进度文件
        progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
        with open(progress_file_path, 'w') as f:
            pass  # 创建空文件

        cmd = [
            ffmpeg_exe, '-y',
            '-progress', progress_file_path,
            '-i', input_path,
            '-c:v', 'libx264',  # 使用软件编码器
            '-crf', '23',       # 默认质量
            '-c:a', 'aac',      # 音频编码
            '-movflags', '+faststart',
            output_path
        ]

        if progress_callback:
            progress_callback("进行基本编码...")

        try:
            # 获取输入视频时长用于进度计算
            try:
                input_duration = self.get_media_duration(input_path)
            except Exception:
                input_duration = None

            # 启动FFmpeg进程
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 注册进程到管理器
            video_ffmpeg_manager.register_process(process)

            # 监控进度
            if progress_callback and input_duration:
                self._monitor_ffmpeg_progress(progress_file_path, input_duration, progress_callback, "基本编码")

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                raise Exception(f"基本编码失败: {stderr}")

            return True

        finally:
            # 注销进程
            video_ffmpeg_manager.unregister_process(process)

            # 清理临时进度文件
            try:
                os.unlink(progress_file_path)
            except Exception:
                pass

    def _final_encode(self, input_path, output_path, settings, progress_callback=None):
        """视频编码输出，支持编码器自动回退"""
        return self._final_encode_with_fallback(input_path, output_path, settings, progress_callback)

    def _final_encode_with_fallback(self, input_path, output_path, settings, progress_callback=None, retry_count=0):
        """视频编码输出，支持编码器回退机制"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        # 检查是否启用自定义压缩
        use_custom_compression = settings and settings.get('custom_compression', False)

        if use_custom_compression:
            # 获取码率设置
            compression_bitrate = settings.get('current_compression_bitrate', 5000)

            # 直接使用码率而不是CRF
            use_bitrate = True
            bitrate_value = f"{compression_bitrate}k"
            crf = None
        else:
            # 不使用自定义压缩，使用默认的CRF设置
            use_bitrate = False
            crf = '23'  # 默认中等质量
            compression_bitrate = None  # 不显示码率

        # 获取最终输出专用编码器设置
        final_settings = settings.copy() if settings else {}
        final_encoder = final_settings.get('final_encoder', 'auto')
        if final_encoder != 'auto':
            final_settings['encoder'] = final_encoder

        # 获取编码设置
        codec, ffmpeg_params = self._get_optimal_encoder_settings(final_settings)

        if progress_callback:
            encoder_type = "NVENC硬件编码器" if 'nvenc' in codec else "软件编码器"
            progress_callback(f"正在生成最终输出... (使用{encoder_type})")

        # 获取输出格式
        output_format = settings.get('format', 'mp4') if settings else 'mp4'

        # 获取输入视频时长
        try:
            input_duration = self.get_media_duration(input_path)
        except Exception:
            input_duration = None

        # 创建临时进度文件
        progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
        with open(progress_file_path, 'w') as f:
            pass  # 创建空文件

        cmd = [
            ffmpeg_exe, '-y',
            '-progress', progress_file_path,  # 输出进度到文件
            '-i', input_path,
            '-c:v', codec,
            '-c:a', 'aac'
        ]

        # 根据压缩设置添加质量参数
        if use_bitrate:
            cmd.extend(['-b:v', bitrate_value])

            # 为码率模式添加兼容性参数，修复跳转问题
            if 'nvenc' in codec:
                # NVIDIA编码器兼容性优化
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",  # 最大码率为目标码率的1.5倍
                    '-bufsize', f"{compression_bitrate * 2}k",         # 缓冲区大小
                    '-rc', 'vbr',                                      # 使用VBR而不是CBR
                    '-g', '60',                                        # 关键帧间隔2秒（30fps）
                    '-keyint_min', '30',                               # 最小关键帧间隔1秒
                    '-bf', '3',                                        # B帧数量
                    '-refs', '3'                                       # 参考帧数量
                ])
            elif 'qsv' in codec:
                # Intel编码器兼容性优化
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{compression_bitrate * 2}k",
                    '-g', '60',
                    '-keyint_min', '30',
                    '-bf', '3'
                ])
            elif 'amf' in codec:
                # AMD编码器兼容性优化
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{compression_bitrate * 2}k",
                    '-g', '60',
                    '-keyint_min', '30'
                ])
            elif codec == 'libx264':
                # 软件编码器兼容性优化
                cmd.extend([
                    '-maxrate', f"{int(compression_bitrate * 1.5)}k",
                    '-bufsize', f"{compression_bitrate * 2}k",
                    '-g', '60',                                        # 关键帧间隔
                    '-keyint_min', '30',                               # 最小关键帧间隔
                    '-bf', '3',                                        # B帧数量
                    '-refs', '3',                                      # 参考帧数量
                    '-profile:v', 'high',                              # 使用high profile
                    '-level', '4.1'                                    # 兼容性级别
                ])
        else:
            cmd.extend(['-crf', crf])

            # 为CRF模式也添加兼容性参数
            if 'nvenc' in codec:
                cmd.extend(['-g', '60', '-keyint_min', '30'])
            elif 'qsv' in codec:
                cmd.extend(['-g', '60', '-keyint_min', '30'])
            elif 'amf' in codec:
                cmd.extend(['-g', '60', '-keyint_min', '30'])
            elif codec == 'libx264':
                cmd.extend(['-g', '60', '-keyint_min', '30', '-profile:v', 'high', '-level', '4.1'])

        # 添加优化参数（在兼容性参数之后）
        cmd.extend(ffmpeg_params)

        # 根据输出格式添加特定参数
        if output_format == 'mp4':
            cmd.extend(['-movflags', '+faststart'])
        elif output_format == 'mkv':
            cmd.extend(['-f', 'matroska'])
        elif output_format == 'avi':
            cmd.extend(['-f', 'avi'])
        elif output_format == 'mov':
            cmd.extend(['-f', 'mov'])

        cmd.append(output_path)

        if progress_callback:
            encoder_type = "NVENC硬件编码器" if 'nvenc' in codec else "软件编码器"
            if use_custom_compression and compression_bitrate is not None:
                progress_callback(f"正在压缩视频... (使用{encoder_type})")
            else:
                progress_callback(f"正在处理视频... (使用{encoder_type})")

        try:
            # 启动FFmpeg进程（隐藏窗口）
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 监控进度
            if progress_callback and input_duration:
                self._monitor_ffmpeg_progress(progress_file_path, input_duration, progress_callback, "视频编码")

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                # 检查是否是硬件编码器失败
                is_hardware_encoder = 'nvenc' in codec

                if is_hardware_encoder and retry_count == 0:
                    # 硬件编码器失败，尝试软件编码器
                    if progress_callback:
                        progress_callback("硬件编码失败，切换到软件编码...")

                    # 清理当前的临时文件
                    try:
                        os.unlink(progress_file_path)
                    except Exception:
                        pass

                    # 强制使用软件编码器重试
                    fallback_settings = settings.copy() if settings else {}
                    fallback_settings['encoder'] = 'libx264'  # 强制使用软件编码器
                    fallback_settings['use_hardware'] = False

                    return self._final_encode_with_fallback(input_path, output_path, fallback_settings, progress_callback, retry_count + 1)
                else:
                    # 软件编码器也失败，或者已经重试过
                    raise Exception(f"视频编码失败: {stderr}")

        finally:
            # 清理临时进度文件
            try:
                os.unlink(progress_file_path)
            except Exception:
                pass

    def _get_optimal_encoder_settings(self, settings):
        """获取最优编码器设置 - 简化版本（仅NVENC和软件编码）"""
        # 获取用户设置
        use_hardware = settings.get('use_hardware_acceleration', True)
        encoder_choice = settings.get('encoder', 'auto')
        fast_mode = settings.get('fast_mode', True)  # 默认启用快速模式
        speed_priority = settings.get('speed_priority', True)  # 速度优先模式
        enable_parallel = settings.get('enable_parallel', True)
        max_threads = settings.get('max_threads', 8)

        # 如果用户指定了编码器
        if encoder_choice != 'auto':
            if self._check_encoder_available(encoder_choice):
                codec = encoder_choice
                # 根据编码器类型设置优化参数
                if 'nvenc' in encoder_choice:
                    # NVIDIA编码器 - 优化设置
                    if speed_priority or fast_mode:
                        ffmpeg_params = [
                            '-preset', 'p1',      # 最快预设
                            '-tune', 'ull',       # 超低延迟
                            '-rc', 'cbr',         # 恒定码率，最快
                            '-g', '60',           # 关键帧间隔
                            '-bf', '0',           # 禁用B帧
                            '-refs', '1'          # 最少参考帧
                        ]
                    else:
                        ffmpeg_params = [
                            '-preset', 'p2',
                            '-rc', 'vbr',
                            '-g', '60',
                            '-bf', '1',
                            '-refs', '2'
                        ]
                else:
                    # 软件编码器 - 优化设置
                    if speed_priority or fast_mode:
                        ffmpeg_params = [
                            '-preset', 'fast',         # 快速预设
                            '-tune', 'zerolatency',     # 零延迟
                            '-g', '60',                 # 关键帧间隔
                            '-bf', '0',                 # 禁用B帧
                            '-refs', '1'                # 最少参考帧
                        ]
                    else:
                        ffmpeg_params = [
                            '-preset', 'medium',
                            '-tune', 'film',
                            '-g', '60',
                            '-bf', '2',
                            '-refs', '3'
                        ]

                    # 软件编码器添加线程参数
                    if enable_parallel and max_threads > 0:
                        ffmpeg_params.extend([
                            '-threads', str(min(max_threads, 8)),  # 限制线程数避免过载
                            '-thread_type', 'frame'                # 只使用帧级并行
                        ])

                return codec, ffmpeg_params
            else:
                print(f"指定的编码器 {encoder_choice} 不可用，回退到自动选择")
                # 如果是硬件编码器失败，强制使用软件编码器
                if 'nvenc' in encoder_choice:
                    use_hardware = False

        # 自动选择编码器 - 简化策略（仅NVENC和软件编码）
        if use_hardware:
            # 尝试NVIDIA编码器（唯一支持的硬件编码器）
            if self._check_encoder_available('h264_nvenc'):
                try:
                    if speed_priority or fast_mode:
                        return 'h264_nvenc', [
                            '-preset', 'p1', '-tune', 'ull', '-rc', 'cbr',
                            '-g', '60', '-bf', '0', '-refs', '1'
                        ]
                    else:
                        return 'h264_nvenc', [
                            '-preset', 'p2', '-profile:v', 'high', '-rc', 'vbr',
                            '-cq', '25', '-g', '60', '-bf', '1', '-refs', '2'
                        ]
                except Exception:
                    print("NVENC编码器不可用，回退到软件编码器")
                    pass

        # 回退到软件编码器
        codec = 'libx264'
        if speed_priority or fast_mode:
            ffmpeg_params = [
                '-preset', 'fast',          # 快速预设
                '-tune', 'zerolatency',     # 零延迟调优
                '-g', '60',                 # 关键帧间隔
                '-bf', '0',                 # 禁用B帧提高速度
                '-refs', '1'                # 最少参考帧
            ]
        else:
            ffmpeg_params = [
                '-preset', 'medium',        # 平衡预设
                '-tune', 'film',            # 电影调优
                '-g', '60',                 # 关键帧间隔
                '-bf', '2',                 # 适量B帧
                '-refs', '3'                # 适量参考帧
            ]

        # 软件编码器添加线程参数
        if enable_parallel and max_threads > 0:
            ffmpeg_params.extend([
                '-threads', str(min(max_threads, 8)),
                '-thread_type', 'frame'
            ])

        return codec, ffmpeg_params

    def _analyze_video_complexity(self, video_path):
        """分析视频复杂度以优化编码参数"""
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return 'medium'

            # 使用ffprobe分析视频特征
            cmd = [
                ffmpeg_exe, '-i', video_path,
                '-vf', 'select=eq(n\\,0)',  # 选择第一帧
                '-f', 'null', '-'
            ]

            result = subprocess.run(cmd, capture_output=True, text=True,
                                  timeout=10, encoding='utf-8', errors='ignore')

            # 简单的复杂度判断（基于文件大小和时长）
            try:
                file_size = os.path.getsize(video_path) / (1024 * 1024)  # MB
                duration = self.get_media_duration(video_path)

                if duration > 0:
                    bitrate_estimate = (file_size * 8) / duration  # Mbps估算

                    if bitrate_estimate > 5:
                        return 'high'
                    elif bitrate_estimate > 2:
                        return 'medium'
                    else:
                        return 'low'

            except Exception:
                pass

            return 'medium'

        except Exception:
            return 'medium'

    def _get_smart_compression_settings(self, settings, video_path=None):
        """智能压缩设置 - 根据内容和用户偏好动态调整"""
        speed_priority = settings.get('speed_priority', True)

        # 基础设置
        if speed_priority:
            # 速度优先 - 使用更激进的压缩
            base_settings = {
                'crf_high_complexity': 28,
                'crf_medium_complexity': 26,
                'crf_low_complexity': 24,
                'preset': 'superfast'
            }
        else:
            # 质量优先 - 更保守的压缩
            base_settings = {
                'crf_high_complexity': 25,
                'crf_medium_complexity': 23,
                'crf_low_complexity': 21,
                'preset': 'fast'
            }

        # 如果提供了视频路径，分析复杂度
        if video_path and os.path.exists(video_path):
            complexity = self._analyze_video_complexity(video_path)

            if complexity == 'high':
                return base_settings['crf_high_complexity'], base_settings['preset']
            elif complexity == 'low':
                return base_settings['crf_low_complexity'], base_settings['preset']
            else:
                return base_settings['crf_medium_complexity'], base_settings['preset']

        # 默认中等复杂度
        return base_settings['crf_medium_complexity'], base_settings['preset']

    def run_performance_benchmark(self, progress_callback=None):
        """运行性能基准测试，提供优化建议"""
        try:
            if progress_callback:
                progress_callback("开始性能基准测试...")

            # 创建测试视频（5秒，简单内容）
            test_video = self._create_test_video()
            if not test_video:
                if progress_callback:
                    progress_callback("无法创建测试视频")
                return None

            results = {}

            # 测试不同编码器的性能
            encoders_to_test = []

            # 检测可用的硬件编码器
            if self._check_encoder_available('h264_nvenc'):
                encoders_to_test.append(('h264_nvenc', '硬件编码器'))
            if self._check_encoder_available('h264_qsv'):
                encoders_to_test.append(('h264_qsv', '硬件编码器'))
            if self._check_encoder_available('h264_amf'):
                encoders_to_test.append(('h264_amf', '硬件编码器'))

            # 总是测试软件编码器
            encoders_to_test.append(('libx264', '软件编码器'))

            if not encoders_to_test:
                if progress_callback:
                    progress_callback("没有可用的编码器")
                return None

            for i, (encoder, name) in enumerate(encoders_to_test):
                if progress_callback:
                    progress_callback(f"正在测试编码器... ({i+1}/{len(encoders_to_test)})")

                # 测试编码速度
                start_time = time.time()
                success = self._test_encoder_performance(test_video, encoder)
                end_time = time.time()

                if success:
                    encode_time = end_time - start_time
                    results[encoder] = {
                        'name': name,
                        'time': encode_time,
                        'fps': 5.0 / encode_time if encode_time > 0 else 0,
                        'available': True
                    }
                else:
                    results[encoder] = {
                        'name': name,
                        'time': float('inf'),
                        'fps': 0,
                        'available': False
                    }

            # 清理测试文件
            try:
                os.unlink(test_video)
            except:
                pass

            if progress_callback:
                progress_callback("性能测试完成")

            # 确保返回有效的结果
            recommendations = self._generate_performance_recommendations(results)
            if not recommendations or not recommendations.get('fastest_encoder'):
                # 如果没有找到可用编码器，返回默认配置
                return {
                    'fastest_encoder': {
                        'encoder': 'libx264',
                        'name': '软件编码器',
                        'time': 0,
                        'fps': 0
                    },
                    'recommended_settings': {},
                    'performance_summary': ['使用默认软件编码器'],
                    'optimization_tips': ['建议检查系统配置']
                }

            return recommendations

        except Exception as e:
            if progress_callback:
                progress_callback(f"性能测试失败: {str(e)}")
            return None

    def _create_test_video(self):
        """创建用于性能测试的简单视频"""
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return None

            # 创建临时测试视频文件
            test_video = self._create_temp_file(suffix='.mp4', prefix='test_video_')

            # 生成5秒的测试视频（简单的彩色条纹）
            cmd = [
                ffmpeg_exe, '-y',
                '-f', 'lavfi',
                '-i', 'testsrc=duration=5:size=1280x720:rate=30',
                '-c:v', 'libx264',
                '-preset', 'ultrafast',
                '-crf', '30',
                test_video
            ]

            # 隐藏FFmpeg窗口
            import platform
            if platform.system() == "Windows":
                result = subprocess.run(cmd, capture_output=True, timeout=30,
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run(cmd, capture_output=True, timeout=30)

            if result.returncode == 0 and os.path.exists(test_video):
                return test_video
            else:
                # 如果创建失败，清理文件
                try:
                    os.unlink(test_video)
                except:
                    pass
                return None

        except Exception:
            return None

    def _test_encoder_performance(self, input_video, encoder):
        """测试特定编码器的性能"""
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return False

            # 使用临时文件而不是null输出，确保兼容性
            output_video = self._create_temp_file(suffix='.mp4', prefix='test_output_')

            # 构建测试命令
            cmd = [
                ffmpeg_exe, '-y',
                '-i', input_video,
                '-c:v', encoder,
                '-t', '3',  # 减少到3秒以加快测试
                '-an',  # 不处理音频
                output_video
            ]

            # 添加编码器特定参数
            if 'nvenc' in encoder:
                cmd.insert(-1, '-preset')
                cmd.insert(-1, 'fast')
            elif 'qsv' in encoder:
                cmd.insert(-1, '-preset')
                cmd.insert(-1, 'fast')
            elif 'amf' in encoder:
                cmd.insert(-1, '-quality')
                cmd.insert(-1, 'speed')
            elif 'libx264' in encoder:
                cmd.insert(-1, '-preset')
                cmd.insert(-1, 'superfast')

            # 隐藏FFmpeg窗口
            import platform
            if platform.system() == "Windows":
                result = subprocess.run(cmd, capture_output=True, timeout=30,
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run(cmd, capture_output=True, timeout=30)

            # 清理临时文件
            try:
                os.unlink(output_video)
            except:
                pass

            return result.returncode == 0

        except Exception:
            return False

    def _generate_performance_recommendations(self, results):
        """根据测试结果生成性能建议"""
        recommendations = {
            'fastest_encoder': None,
            'recommended_settings': {},
            'performance_summary': [],
            'optimization_tips': []
        }

        # 找出最快的可用编码器
        available_results = {k: v for k, v in results.items() if v['available']}

        if available_results:
            fastest = min(available_results.items(), key=lambda x: x[1]['time'])
            recommendations['fastest_encoder'] = {
                'encoder': fastest[0],
                'name': fastest[1]['name'],
                'time': fastest[1]['time'],
                'fps': fastest[1]['fps']
            }

        # 生成性能摘要
        for encoder, data in results.items():
            if data['available']:
                recommendations['performance_summary'].append(
                    f"{data['name']}: {data['time']:.2f}秒 ({data['fps']:.1f} FPS)"
                )
            else:
                recommendations['performance_summary'].append(
                    f"{data['name']}: 不可用"
                )

        # 生成优化建议
        if recommendations['fastest_encoder']:
            fastest_name = recommendations['fastest_encoder']['name']
            recommendations['optimization_tips'].append(
                f"建议使用 {fastest_name} 以获得最佳性能"
            )

        # 硬件加速建议
        hw_encoders = [k for k, v in results.items() if 'nvenc' in k or 'qsv' in k or 'amf' in k]
        available_hw = [k for k in hw_encoders if results[k]['available']]

        if not available_hw:
            recommendations['optimization_tips'].append(
                "未检测到硬件加速，建议检查显卡驱动"
            )

        # 线程数建议
        import multiprocessing
        cpu_count = multiprocessing.cpu_count()
        if cpu_count >= 8:
            recommendations['optimization_tips'].append(
                "检测到多核处理器，建议启用多线程处理"
            )

        return recommendations

    def _check_encoder_available(self, encoder):
        """检查编码器是否可用"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            return False

        try:
            # 首先检查编码器是否在列表中
            cmd = [ffmpeg_exe, '-encoders']
            # 隐藏FFmpeg窗口
            import platform
            if platform.system() == "Windows":
                result = subprocess.run(cmd, capture_output=True, text=True,
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run(cmd, capture_output=True, text=True)

            if encoder not in result.stdout:
                return False

            # 对于NVENC硬件编码器，进行实际测试
            if 'nvenc' in encoder:
                return self._test_hardware_encoder(encoder)

            return True
        except Exception:
            return False

    def _test_hardware_encoder(self, encoder):
        """实际测试硬件编码器是否可用"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            return False

        try:
            # 创建一个简单的测试视频来验证编码器
            temp_output = self._create_temp_file(suffix='.mp4', prefix='encoder_test_')

            try:
                # 使用testsrc生成1秒的测试视频
                cmd = [
                    ffmpeg_exe, '-y',
                    '-f', 'lavfi',
                    '-i', 'testsrc=duration=1:size=320x240:rate=1',
                    '-c:v', encoder,
                    '-t', '1',
                    '-f', 'mp4',
                    temp_output
                ]

                import platform
                if platform.system() == "Windows":
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10,
                                          creationflags=subprocess.CREATE_NO_WINDOW)
                else:
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

                success = result.returncode == 0

                # 清理测试文件
                try:
                    import os
                    os.unlink(temp_output)
                except Exception:
                    pass

                return success

            except Exception:
                # 清理测试文件
                try:
                    import os
                    os.unlink(temp_output)
                except Exception:
                    pass
                return False

        except Exception:
            return False

    def _detect_hardware_encoders(self):
        """检测可用的硬件编码器（仅NVENC）"""
        hardware_encoders = []

        # 只检测NVENC编码器
        if self._check_encoder_available('h264_nvenc'):
            hardware_encoders.append('h264_nvenc')

        return hardware_encoders

    def test_encoder_performance(self, test_video_path=None, progress_callback=None):
        """
        测试不同编码器的性能

        参数:
            test_video_path (str): 测试视频路径，如果为None则创建临时测试视频
            progress_callback (function): 进度回调函数

        返回:
            dict: 编码器性能测试结果
        """
        import time
        import tempfile
        import os

        if progress_callback:
            progress_callback("开始性能测试...")

        # 准备测试视频
        if test_video_path is None or not os.path.exists(test_video_path):
            # 创建临时测试视频
            test_video_path = self._create_test_video(progress_callback)
            if not test_video_path:
                return {"error": "无法创建测试视频"}
            cleanup_test_video = True
        else:
            cleanup_test_video = False

        # 测试的编码器列表（仅NVENC和软件编码）
        test_encoders = [
            ('libx264', '软件编码器'),
            ('h264_nvenc', 'NVENC硬件编码器')
        ]

        results = {}
        temp_dir = self._get_temp_dir()
        if not temp_dir:
            import tempfile
            temp_dir = tempfile.mkdtemp()
        else:
            import uuid
            temp_dir = os.path.join(temp_dir, f"encoder_test_{uuid.uuid4().hex[:8]}")
            os.makedirs(temp_dir, exist_ok=True)

        try:
            for i, (encoder, display_name) in enumerate(test_encoders):
                if progress_callback:
                    progress_callback(f"正在测试编码器... ({i+1}/{len(test_encoders)})")

                # 检查编码器是否可用
                if not self._check_encoder_available(encoder):
                    results[encoder] = {
                        'name': display_name,
                        'available': False,
                        'time': None,
                        'error': '编码器不可用'
                    }
                    continue

                # 测试编码器性能
                output_path = os.path.join(temp_dir, f"test_{encoder}.mp4")
                start_time = time.time()

                try:
                    success = self._test_single_encoder(test_video_path, output_path, encoder)
                    end_time = time.time()

                    if success:
                        encode_time = end_time - start_time
                        results[encoder] = {
                            'name': display_name,
                            'available': True,
                            'time': encode_time,
                            'error': None
                        }
                        if progress_callback:
                            progress_callback(f"✓ {display_name}: {encode_time:.2f}秒")
                    else:
                        results[encoder] = {
                            'name': display_name,
                            'available': True,
                            'time': None,
                            'error': '编码失败'
                        }
                        if progress_callback:
                            progress_callback(f"✗ {display_name}: 编码失败")

                except Exception as e:
                    results[encoder] = {
                        'name': display_name,
                        'available': True,
                        'time': None,
                        'error': str(e)
                    }
                    if progress_callback:
                        progress_callback(f"✗ {display_name}: {str(e)}")

                # 清理临时输出文件
                try:
                    if os.path.exists(output_path):
                        os.unlink(output_path)
                except Exception:
                    pass

        finally:
            # 清理临时目录
            try:
                import shutil
                shutil.rmtree(temp_dir)
            except Exception:
                pass

            # 清理测试视频
            if cleanup_test_video and test_video_path:
                try:
                    os.unlink(test_video_path)
                except Exception:
                    pass

        if progress_callback:
            progress_callback("编码器性能测试完成")

        return results

    def _create_test_video(self, progress_callback=None):
        """创建用于测试的临时视频"""
        import tempfile
        import os

        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            return None

        if progress_callback:
            progress_callback("创建测试视频...")

        # 创建临时文件
        temp_video_path = self._create_temp_file(suffix='.mp4', prefix='test_video_')

        try:
            # 创建10秒的测试视频（纯色+噪声）
            cmd = [
                ffmpeg_exe, '-y',
                '-f', 'lavfi',
                '-i', 'testsrc=duration=10:size=1280x720:rate=30',
                '-c:v', 'libx264',
                '-preset', 'ultrafast',
                '-crf', '23',
                '-t', '10',
                temp_video_path
            ]

            # 隐藏FFmpeg窗口
            import platform
            if platform.system() == "Windows":
                result = subprocess.run(cmd, capture_output=True,
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run(cmd, capture_output=True)

            if result.returncode == 0:
                return temp_video_path
            else:
                # 清理失败的文件
                self._cleanup_temp_file(temp_video_path)
                return None

        except Exception:
            # 清理失败的文件
            self._cleanup_temp_file(temp_video_path)
            return None

    def _test_single_encoder(self, input_path, output_path, encoder):
        """测试单个编码器的性能"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            return False

        try:
            # 构建测试命令（编码5秒视频）
            cmd = [
                ffmpeg_exe, '-y',
                '-i', input_path,
                '-c:v', encoder,
                '-t', '5',  # 只编码5秒以加快测试
                '-an',  # 不处理音频
                output_path
            ]

            # 根据编码器类型添加特定参数
            if encoder == 'libx264':
                cmd.insert(-1, '-preset')
                cmd.insert(-1, 'fast')
            elif 'nvenc' in encoder:
                cmd.insert(-1, '-preset')
                cmd.insert(-1, 'fast')
            elif 'qsv' in encoder:
                cmd.insert(-1, '-preset')
                cmd.insert(-1, 'fast')
            elif 'amf' in encoder:
                cmd.insert(-1, '-quality')
                cmd.insert(-1, 'speed')

            # 隐藏FFmpeg窗口并设置超时
            import platform
            if platform.system() == "Windows":
                result = subprocess.run(cmd, capture_output=True, timeout=30,
                                      creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run(cmd, capture_output=True, timeout=30)

            return result.returncode == 0

        except subprocess.TimeoutExpired:
            return False
        except Exception:
            return False





    def _detect_audio_codec(self, audio_path):
        """检测音频文件的编码格式"""
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return None

            # 构建ffprobe路径
            ffprobe_exe = ffmpeg_exe.replace('ffmpeg.exe', 'ffprobe.exe')
            if not os.path.exists(ffprobe_exe):
                # 尝试其他可能的路径
                ffmpeg_dir = os.path.dirname(ffmpeg_exe)
                ffprobe_exe = os.path.join(ffmpeg_dir, 'ffprobe.exe')
                if not os.path.exists(ffprobe_exe):
                    # 使用ffmpeg进行检测
                    return self._detect_audio_codec_with_ffmpeg(audio_path)

            # 使用ffprobe检测音频编码格式
            cmd = [
                ffprobe_exe, '-v', 'quiet',
                '-select_streams', 'a:0',
                '-show_entries', 'stream=codec_name',
                '-of', 'csv=p=0',
                audio_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=10)

            if result.returncode == 0 and result.stdout.strip():
                codec = result.stdout.strip()
                return codec

        except Exception as e:
            print(f"音频格式检测失败: {e}")

        return None

    def _detect_audio_codec_with_ffmpeg(self, audio_path):
        """使用ffmpeg检测音频编码格式（备用方法）"""
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return None

            # 使用ffmpeg -i 获取文件信息
            cmd = [ffmpeg_exe, '-i', audio_path, '-f', 'null', '-']

            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=10)

            # 解析stderr中的音频信息
            if result.stderr:
                lines = result.stderr.split('\n')
                for line in lines:
                    if 'Audio:' in line:
                        # 提取编码格式，例如: "Audio: aac (LC) (mp4a / 0x6134706D)"
                        parts = line.split('Audio:')[1].strip().split()
                        if parts:
                            codec = parts[0].split('(')[0].strip()
                            return codec

        except Exception as e:
            print(f"FFmpeg音频格式检测失败: {e}")

        return None

    def _test_video_playability(self, video_path):
        """测试视频是否可以正常播放"""
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return False

            # 使用ffprobe检查视频完整性
            ffprobe_exe = ffmpeg_exe.replace('ffmpeg.exe', 'ffprobe.exe')
            if not os.path.exists(ffprobe_exe):
                ffprobe_exe = os.path.join(os.path.dirname(ffmpeg_exe), 'ffprobe.exe')

            if os.path.exists(ffprobe_exe):
                cmd = [
                    ffprobe_exe, '-v', 'error',
                    '-select_streams', 'v:0',
                    '-show_entries', 'stream=codec_name,width,height,duration',
                    '-of', 'csv=p=0',
                    video_path
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=10)
                return result.returncode == 0 and result.stdout.strip()
            else:
                # 备用方法：尝试读取视频帧
                cmd = [
                    ffmpeg_exe, '-v', 'error',
                    '-i', video_path,
                    '-t', '1',
                    '-f', 'null', '-'
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=10)
                return result.returncode == 0

        except Exception:
            return False

    def _monitor_ffmpeg_progress(self, progress_file, total_duration, progress_callback, operation_name, process=None):
        """监控FFmpeg进度（增强版：实时错误检测和进程状态监控）"""
        import threading
        import time
        import psutil

        # 导入错误日志记录器
        try:
            from common.error_logger import error_logger
        except ImportError:
            error_logger = None

        # 共享状态
        monitor_state = {
            'should_stop': False,
            'error_detected': False,
            'error_message': None
        }

        def stderr_monitor():
            """实时监控stderr流"""
            if not process or not hasattr(process, 'stderr'):
                return

            try:
                while not monitor_state['should_stop'] and process.poll() is None:
                    if process.stderr and process.stderr.readable():
                        line = process.stderr.readline()
                        if line:
                            line = line.strip()
                            # 检测常见的FFmpeg错误
                            if self._is_ffmpeg_error(line):
                                monitor_state['error_detected'] = True
                                monitor_state['error_message'] = line

                                # 立即记录错误
                                if error_logger:
                                    error_logger.log_general_error(
                                        f"{operation_name}_实时错误",
                                        line,
                                        f"进程PID: {process.pid if process else 'Unknown'}"
                                    )

                                if progress_callback:
                                    progress_callback(f"❌ 检测到FFmpeg错误: {line}")
                                break
                    time.sleep(0.1)
            except Exception as e:
                print(f"stderr监控错误: {e}")

        def process_health_monitor():
            """监控进程健康状态"""
            if not process:
                return

            try:
                last_cpu_check = time.time()
                low_cpu_count = 0

                while not monitor_state['should_stop'] and process.poll() is None:
                    current_time = time.time()

                    # 每5秒检查一次CPU使用率
                    if current_time - last_cpu_check >= 5:
                        try:
                            if hasattr(process, 'pid') and process.pid:
                                proc = psutil.Process(process.pid)
                                cpu_percent = proc.cpu_percent(interval=1)

                                # 如果CPU使用率连续低于1%，可能进程卡住了
                                if cpu_percent < 1.0:
                                    low_cpu_count += 1
                                    if low_cpu_count >= 3:  # 连续3次检查都是低CPU
                                        monitor_state['error_detected'] = True
                                        monitor_state['error_message'] = f"进程可能卡住 (CPU: {cpu_percent:.1f}%)"

                                        if progress_callback:
                                            progress_callback(f"⚠️ 进程可能卡住 (CPU使用率: {cpu_percent:.1f}%)")
                                        break
                                else:
                                    low_cpu_count = 0

                                last_cpu_check = current_time
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            break

                    time.sleep(2)
            except Exception as e:
                print(f"进程健康监控错误: {e}")

        def progress_monitor():
            """监控进度文件"""
            last_time = 0
            last_percent = 0
            start_time = time.time()
            last_progress_time = start_time
            no_progress_timeout = 120  # 2分钟无进度则超时（缩短超时时间）
            last_progress_msg = None

            # 重置时间估算历史
            if hasattr(self, '_time_estimates'):
                delattr(self, '_time_estimates')

            while not monitor_state['should_stop']:
                try:
                    current_loop_time = time.time()

                    # 检查进程是否已完成
                    if process and process.poll() is not None:
                        if progress_callback:
                            if process.returncode == 0:
                                progress_callback(f"✅ {operation_name}: 进程已完成")
                            else:
                                progress_callback(f"❌ {operation_name}: 进程异常退出 (返回码: {process.returncode})")
                        monitor_state['should_stop'] = True
                        break

                    # 检查是否有错误被检测到
                    if monitor_state['error_detected']:
                        if progress_callback:
                            progress_callback(f"❌ 停止监控: {monitor_state['error_message']}")
                        break

                    # 检查进度文件是否存在
                    if os.path.exists(progress_file):
                        try:
                            # 使用更安全的文件读取方式
                            with open(progress_file, 'r', encoding='utf-8', errors='ignore') as f:
                                content = f.read()
                        except (IOError, OSError):
                            # 文件可能正在被写入，稍后重试
                            time.sleep(0.1)
                            continue

                        # 解析进度信息
                        lines = content.strip().split('\n')
                        current_time = 0
                        speed = None
                        progress_found = False

                        for line in lines:
                            line = line.strip()
                            if line.startswith('out_time_ms='):
                                try:
                                    time_ms = int(line.split('=')[1])
                                    current_time = time_ms / 1000000.0  # 转换为秒
                                    progress_found = True
                                except (ValueError, IndexError):
                                    continue
                            elif line.startswith('out_time='):
                                try:
                                    time_str = line.split('=')[1]
                                    current_time = self._parse_time_string(time_str)
                                    progress_found = True
                                except (IndexError, ValueError):
                                    continue
                            elif line.startswith('speed='):
                                try:
                                    speed_str = line.split('=')[1].replace('x', '').strip()
                                    if speed_str and speed_str != 'N/A':
                                        speed = float(speed_str)
                                except (ValueError, IndexError):
                                    speed = None

                        # 计算进度百分比
                        if current_time > last_time and total_duration > 0:
                            progress_percent = min(100, (current_time / total_duration) * 100)
                            last_progress_time = current_loop_time  # 更新最后进度时间

                            # 只在进度有明显变化时更新（避免过于频繁的更新）
                            if progress_percent - last_percent >= 1.0 or progress_percent >= 99.5:
                                elapsed_time = current_loop_time - start_time

                                # 构建进度消息
                                progress_msg = f"{operation_name}: {progress_percent:.1f}%"

                                # 添加时间信息
                                if speed and speed > 0:
                                    progress_msg += f" (速度: {speed:.1f}x)"

                                # 添加预计剩余时间（改进的预测算法）
                                if progress_percent > 5 and elapsed_time > 3:  # 降低阈值，更早显示预估时间
                                    # 使用加权平均来平滑预测，减少波动
                                    if not hasattr(self, '_time_estimates'):
                                        self._time_estimates = []

                                    # 计算当前的时间估算
                                    if progress_percent > 0:
                                        current_estimate = elapsed_time / (progress_percent / 100)
                                        self._time_estimates.append(current_estimate)

                                        # 只保留最近的5个估算值
                                        if len(self._time_estimates) > 5:
                                            self._time_estimates.pop(0)

                                        # 使用加权平均（最近的估算权重更高）
                                        weights = [1, 1.2, 1.5, 1.8, 2.0][-len(self._time_estimates):]
                                        weighted_sum = sum(est * weight for est, weight in zip(self._time_estimates, weights))
                                        weight_sum = sum(weights)
                                        estimated_total = weighted_sum / weight_sum

                                        remaining_time = estimated_total - elapsed_time

                                        # 对于接近完成的任务，减少预测时间（因为通常会加速）
                                        if progress_percent > 80:
                                            remaining_time *= 0.7  # 减少30%
                                        elif progress_percent > 60:
                                            remaining_time *= 0.85  # 减少15%

                                        if remaining_time > 0:
                                            remaining_min = int(remaining_time // 60)
                                            remaining_sec = int(remaining_time % 60)
                                            progress_msg += f" (预计剩余: {remaining_min:02d}:{remaining_sec:02d})"

                                if progress_callback:
                                    progress_callback(progress_msg)
                                    last_progress_msg = progress_msg
                                last_percent = progress_percent

                            last_time = current_time

                            # 如果接近完成，退出监控
                            if progress_percent >= 99.8:
                                monitor_state['should_stop'] = True
                                break

                        # 检查是否长时间无进度更新
                        elif progress_found and (current_loop_time - last_progress_time) > no_progress_timeout:
                            # 记录超时到错误日志
                            if error_logger:
                                error_logger.log_progress_timeout(
                                    operation_name,
                                    current_loop_time - last_progress_time,
                                    last_progress_msg
                                )

                            monitor_state['error_detected'] = True
                            monitor_state['error_message'] = f"进度超时 ({no_progress_timeout}秒无更新)"

                            if progress_callback:
                                progress_callback(f"⚠️ {operation_name}: 进度监控超时，可能卡住了")
                                progress_callback(f"🔄 正在终止卡住的进程...")

                            # 主动终止卡住的进程（强力版本）
                            if process and process.poll() is None:
                                self._force_terminate_process(process, progress_callback)

                            break

                    # 检查总体超时（防止无限等待）
                    elif (current_loop_time - start_time) > 1800:  # 30分钟总体超时（缩短）
                        if error_logger:
                            error_logger.log_general_error(
                                operation_name,
                                "总体超时（30分钟）",
                                f"进度文件: {progress_file}"
                            )

                        monitor_state['error_detected'] = True
                        monitor_state['error_message'] = "总体超时（30分钟）"

                        # 静默处理总体超时（仅记录到错误日志，不在界面提示）
                        # if progress_callback:
                        #     progress_callback(f"❌ {operation_name}: 总体超时")
                        break

                    time.sleep(0.5)  # 增加检查间隔，减少CPU占用

                except Exception as e:
                    # 记录监控错误到日志
                    if error_logger:
                        error_logger.log_general_error(
                            f"{operation_name}_监控",
                            str(e),
                            f"进度文件: {progress_file}"
                        )

                    print(f"进度监控错误: {e}")
                    time.sleep(1)  # 出错后等待更长时间再重试
                    continue

        # 启动所有监控线程
        threads = []

        # 进度监控线程
        progress_thread = threading.Thread(target=progress_monitor, daemon=True)
        progress_thread.start()
        threads.append(progress_thread)

        # 如果有进程对象，启动额外的监控
        if process:
            # stderr监控线程
            stderr_thread = threading.Thread(target=stderr_monitor, daemon=True)
            stderr_thread.start()
            threads.append(stderr_thread)

            # 进程健康监控线程
            health_thread = threading.Thread(target=process_health_monitor, daemon=True)
            health_thread.start()
            threads.append(health_thread)

        # 返回监控状态，供外部检查
        return monitor_state, threads

    def _is_ffmpeg_error(self, line):
        """检测FFmpeg错误信息"""
        if not line:
            return False

        line_lower = line.lower()

        # 常见的FFmpeg错误模式
        error_patterns = [
            'error',
            'failed',
            'cannot',
            'unable',
            'invalid',
            'not found',
            'permission denied',
            'no such file',
            'codec not found',
            'format not supported',
            'out of memory',
            'segmentation fault',
            'access violation',
            'assertion failed',
            'corrupted',
            'broken pipe'
        ]

        # 检查是否包含错误模式
        for pattern in error_patterns:
            if pattern in line_lower:
                # 排除一些误报
                if 'deprecated' in line_lower or 'warning' in line_lower:
                    continue
                return True

        return False

    def _build_safe_encoding_params(self, settings, codec, progress_callback=None):
        """
        构建极简的编码参数（最小化复杂度，避免卡住）

        参数:
            settings (dict): 用户设置
            codec (str): 编码器名称
            progress_callback (function): 进度回调

        返回:
            list: FFmpeg参数列表
        """
        params = []

        try:
            # 检查是否启用自定义设置
            use_custom_resolution = settings and settings.get('custom_resolution', False)
            use_custom_compression = settings and settings.get('custom_compression', False)

            # 处理分辨率设置（极简方式）
            if use_custom_resolution:
                width = settings.get('width', 1920)
                height = settings.get('height', 1080)

                # 确保分辨率是偶数
                width = width if width % 2 == 0 else width + 1
                height = height if height % 2 == 0 else height + 1

                # 只添加最基本的缩放参数
                params.extend(['-vf', f'scale={width}:{height}'])
                if progress_callback:
                    progress_callback(f"应用分辨率: {width}x{height}")

            # 处理压缩设置（极简方式）
            if use_custom_compression:
                compression_bitrate = settings.get('current_compression_bitrate')

                # 如果没有current_compression_bitrate，尝试从其他设置获取
                if compression_bitrate is None:
                    compression_bitrate = settings.get('first_compression_bitrate', settings.get('other_compression_bitrate', 5000))

                compression_bitrate = max(1000, min(20000, compression_bitrate))  # 更保守的范围

                # 只添加最基本的码率参数
                params.extend(['-b:v', f'{compression_bitrate}k'])
                if progress_callback:
                    progress_callback(f"应用码率: {compression_bitrate}k")
            else:
                # 默认使用CRF
                params.extend(['-crf', '23'])

            # 只添加最基本的编码器参数
            if 'libx264' in codec:
                params.extend(['-preset', 'fast'])  # 使用fast预设，更快更稳定
            elif 'nvenc' in codec:
                params.extend(['-preset', 'fast'])
            elif 'qsv' in codec:
                params.extend(['-preset', 'fast'])
            elif 'amf' in codec:
                params.extend(['-quality', 'speed'])

            # 只添加最必要的稳定性参数
            params.extend(['-pix_fmt', 'yuv420p'])

            return params

        except Exception as e:
            if progress_callback:
                progress_callback(f"⚠️ 使用默认参数: {e}")

            # 返回最基本的参数
            return ['-preset', 'fast', '-crf', '23', '-pix_fmt', 'yuv420p']

    def _get_stable_nvenc_settings(self, settings=None, progress_callback=None):
        """
        获取稳定的NVENC编码器设置（基于最佳实践）

        参数:
            settings (dict): 用户设置
            progress_callback (function): 进度回调

        返回:
            tuple: (编码器名称, 参数列表)
        """
        try:
            # 检查NVENC可用性
            if not self._is_nvenc_available():
                if progress_callback:
                    progress_callback("NVENC不可用，使用软件编码器")
                return self._get_stable_software_settings(settings, progress_callback)

            # 使用稳定的NVENC参数（基于最佳实践）
            codec = 'h264_nvenc'

            # 基础稳定参数
            params = [
                '-preset', 'medium',        # 使用medium预设（稳定性和性能的平衡）
                '-rc', 'vbr',              # 使用VBR模式（比CBR更稳定）
                '-cq', '23',               # 恒定质量（类似CRF）
                '-qmin', '18',             # 最小量化参数
                '-qmax', '28',             # 最大量化参数
                '-g', '250',               # 关键帧间隔（标准值）
                '-bf', '3',                # B帧数量（标准值）
                '-b_ref_mode', 'middle',   # B帧参考模式
                '-temporal_aq', '1',       # 时间自适应量化
                '-spatial_aq', '1',        # 空间自适应量化
                '-aq-strength', '8',       # AQ强度
                '-pix_fmt', 'yuv420p'      # 像素格式
            ]

            # 当启用自定义压缩时，去掉与码率控制冲突的质量参数（如 -cq/-qmin/-qmax）
            if settings and settings.get('custom_compression', False):
                cleaned = []
                skip_next = False
                for i, p in enumerate(params):
                    if skip_next:
                        skip_next = False
                        continue
                    if p in ['-cq', '-qmin', '-qmax']:
                        skip_next = True
                        continue
                    cleaned.append(p)
                params = cleaned

            # 添加自定义设置（基于FFmpeg最佳实践）
            if settings:
                # 自定义分辨率（支持保持纵横比）
                if settings.get('custom_resolution', False):
                    width = settings.get('width', 1920)
                    height = settings.get('height', 1080)
                    keep_aspect_ratio = settings.get('keep_aspect_ratio', True)

                    # 使用统一的智能缩放过滤器
                    scale_filter = self._build_smart_scale_filter(width, height, progress_callback, keep_aspect_ratio)
                    if scale_filter:
                        params.extend(['-vf', scale_filter])

                # 自定义压缩（优化的VBR模式）
                if settings.get('custom_compression', False):
                    compression_bitrate = settings.get('current_compression_bitrate')

                    # 调试信息：显示实际的设置值
                    if progress_callback:
                        progress_callback(f"🔍 调试信息 - 自定义压缩: {settings.get('custom_compression')}")
                        progress_callback(f"🔍 调试信息 - 当前码率设置: {compression_bitrate}")
                        progress_callback(f"🔍 调试信息 - 第一集码率: {settings.get('first_compression_bitrate')}")
                        progress_callback(f"🔍 调试信息 - 其他集码率: {settings.get('other_compression_bitrate')}")

                    # 如果没有current_compression_bitrate，尝试从其他设置获取
                    if compression_bitrate is None:
                        # 默认使用第一集码率，如果没有则使用其他集码率
                        compression_bitrate = settings.get('first_compression_bitrate', settings.get('other_compression_bitrate', 5000))
                        if progress_callback:
                            progress_callback(f"⚠️ 未找到current_compression_bitrate，使用备用码率: {compression_bitrate}k")

                    compression_bitrate = max(1000, min(20000, compression_bitrate))

                    # 使用优化的VBR模式（基于最佳实践）
                    params.extend([
                        '-b:v', f'{compression_bitrate}k',
                        '-maxrate', f'{int(compression_bitrate * 1.5)}k',
                        '-bufsize', f'{int(compression_bitrate * 2)}k'
                    ])
                    if progress_callback:
                        progress_callback(f"应用VBR码率: {compression_bitrate}k")

            if progress_callback:
                progress_callback("使用稳定的NVENC硬件编码器")

            return codec, params

        except Exception as e:
            if progress_callback:
                progress_callback(f"NVENC设置失败，使用软件编码器: {e}")
            return self._get_stable_software_settings(settings, progress_callback)

    def _get_stable_software_settings(self, settings=None, progress_callback=None):
        """
        获取稳定的软件编码器设置

        参数:
            settings (dict): 用户设置
            progress_callback (function): 进度回调

        返回:
            tuple: (编码器名称, 参数列表)
        """
        codec = 'libx264'

        # 基础稳定参数
        params = [
            '-preset', 'fast',          # 快速预设
            '-crf', '23',               # 恒定质量
            '-g', '250',                # 关键帧间隔
            '-bf', '3',                 # B帧数量
            '-refs', '3',               # 参考帧数量
            '-pix_fmt', 'yuv420p',      # 像素格式
            '-threads', '0'             # 自动线程数
        ]

        # 添加自定义设置
        if settings:
            # 自定义分辨率
            if settings.get('custom_resolution', False):
                width = settings.get('width', 1920)
                height = settings.get('height', 1080)
                # 确保分辨率是偶数
                width = width if width % 2 == 0 else width + 1
                height = height if height % 2 == 0 else height + 1
                params.extend(['-vf', f'scale={width}:{height}'])
                if progress_callback:
                    progress_callback(f"应用自定义分辨率: {width}x{height}")

            # 自定义压缩
            if settings.get('custom_compression', False):
                compression_bitrate = settings.get('current_compression_bitrate')

                # 如果没有current_compression_bitrate，尝试从其他设置获取
                if compression_bitrate is None:
                    compression_bitrate = settings.get('first_compression_bitrate', settings.get('other_compression_bitrate', 5000))

                compression_bitrate = max(1000, min(20000, compression_bitrate))
                # 移除CRF，使用码率控制
                params = [p for p in params if p != '-crf' and (params.index(p) == 0 or params[params.index(p)-1] != '-crf')]
                params.extend(['-b:v', f'{compression_bitrate}k'])
                if progress_callback:
                    progress_callback(f"应用自定义码率: {compression_bitrate}k")

        if progress_callback:
            progress_callback("使用稳定的软件编码器")

        return codec, params

    def _is_nvenc_available(self):
        """检查NVENC是否可用"""
        try:
            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return False

            import subprocess
            result = subprocess.run([ffmpeg_exe, '-encoders'],
                                  capture_output=True, text=True, timeout=10)
            return 'h264_nvenc' in result.stdout
        except:
            return False

    def _build_smart_scale_filter(self, target_width, target_height, progress_callback=None, keep_aspect_ratio=True):
        """
        构建智能缩放过滤器（基于FFmpeg最佳实践）

        参数:
            target_width (int): 目标宽度
            target_height (int): 目标高度
            progress_callback (function): 进度回调
            keep_aspect_ratio (bool): 是否保持纵横比

        返回:
            str: 缩放过滤器字符串
        """
        try:
            # 确保目标分辨率是偶数
            if target_width > 0:
                target_width = target_width if target_width % 2 == 0 else target_width + 1
            if target_height > 0:
                target_height = target_height if target_height % 2 == 0 else target_height + 1

            # 根据指定的维度构建缩放过滤器
            if target_width > 0 and target_height > 0:
                # 两个维度都指定
                if keep_aspect_ratio:
                    # 保持纵横比，使用force_original_aspect_ratio
                    scale_filter = f'scale={target_width}:{target_height}:force_original_aspect_ratio=decrease'
                    if progress_callback:
                        progress_callback(f"应用智能缩放: {target_width}x{target_height} (保持纵横比)")
                else:
                    # 强制拉伸到指定分辨率
                    scale_filter = f'scale={target_width}:{target_height}'
                    if progress_callback:
                        progress_callback(f"应用强制缩放: {target_width}x{target_height} (可能拉伸)")

                return scale_filter

            elif target_width > 0:
                # 只指定宽度，高度自动计算
                scale_filter = f'scale={target_width}:-1'

                if progress_callback:
                    progress_callback(f"应用宽度缩放: {target_width}px (自动计算高度)")

                return scale_filter

            elif target_height > 0:
                # 只指定高度，宽度自动计算
                scale_filter = f'scale=-1:{target_height}'

                if progress_callback:
                    progress_callback(f"应用高度缩放: {target_height}px (自动计算宽度)")

                return scale_filter

            else:
                if progress_callback:
                    progress_callback("⚠️ 无效的分辨率设置，跳过缩放")
                return None

        except Exception as e:
            if progress_callback:
                progress_callback(f"⚠️ 缩放过滤器构建失败: {e}")
            return None

    def _get_optimal_scale_method(self, source_width, source_height, target_width, target_height):
        """
        获取最优的缩放方法

        参数:
            source_width (int): 源宽度
            source_height (int): 源高度
            target_width (int): 目标宽度
            target_height (int): 目标高度

        返回:
            str: 缩放方法建议
        """
        try:
            if not all([source_width, source_height, target_width, target_height]):
                return "基础缩放"

            # 计算缩放比例
            width_ratio = target_width / source_width
            height_ratio = target_height / source_height

            # 判断缩放类型
            if abs(width_ratio - height_ratio) < 0.01:
                # 等比例缩放
                if width_ratio > 1:
                    return "等比例放大"
                elif width_ratio < 1:
                    return "等比例缩小"
                else:
                    return "无需缩放"
            else:
                # 非等比例缩放（可能拉伸）
                return "非等比例缩放（可能拉伸）"

        except:
            return "未知缩放类型"

    def _force_terminate_process(self, process, progress_callback=None):
        """
        强力终止进程（多种方法确保终止成功）

        参数:
            process: 要终止的进程对象
            progress_callback: 进度回调函数
        """
        import threading
        import time
        import subprocess
        import os

        if not process or process.poll() is not None:
            return  # 进程已经结束

        pid = process.pid

        if progress_callback:
            progress_callback(f"🔄 开始强力终止进程 PID {pid}")

        # 方法1：标准terminate（非阻塞）
        try:
            if progress_callback:
                progress_callback(f"🔄 尝试优雅终止 PID {pid}")

            process.terminate()

            # 非阻塞等待，最多等待3秒
            for i in range(30):  # 3秒，每0.1秒检查一次
                if process.poll() is not None:
                    if progress_callback:
                        progress_callback(f"✅ 进程 PID {pid} 已优雅终止")
                    return
                time.sleep(0.1)

        except Exception as e:
            if progress_callback:
                progress_callback(f"⚠️ 优雅终止失败: {e}")

        # 方法2：强制kill（非阻塞）
        try:
            if progress_callback:
                progress_callback(f"💀 尝试强制kill PID {pid}")

            process.kill()

            # 非阻塞等待，最多等待2秒
            for i in range(20):  # 2秒，每0.1秒检查一次
                if process.poll() is not None:
                    if progress_callback:
                        progress_callback(f"✅ 进程 PID {pid} 已强制终止")
                    return
                time.sleep(0.1)

        except Exception as e:
            if progress_callback:
                progress_callback(f"⚠️ 强制kill失败: {e}")

        # 方法3：系统级taskkill（Windows）
        try:
            if progress_callback:
                progress_callback(f"💀 使用系统级taskkill终止 PID {pid}")

            # 使用taskkill强制终止进程树
            result = subprocess.run([
                'taskkill', '/F', '/T', '/PID', str(pid)
            ], capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                if progress_callback:
                    progress_callback(f"✅ 系统级终止成功 PID {pid}")
                return
            else:
                if progress_callback:
                    progress_callback(f"⚠️ taskkill失败: {result.stderr}")

        except Exception as e:
            if progress_callback:
                progress_callback(f"⚠️ 系统级终止失败: {e}")

        # 方法4：psutil强制终止（如果可用）
        try:
            import psutil

            if progress_callback:
                progress_callback(f"💀 使用psutil强制终止 PID {pid}")

            # 获取进程及其子进程
            try:
                parent = psutil.Process(pid)
                children = parent.children(recursive=True)

                # 先终止子进程
                for child in children:
                    try:
                        child.kill()
                        if progress_callback:
                            progress_callback(f"💀 终止子进程 PID {child.pid}")
                    except:
                        pass

                # 再终止父进程
                parent.kill()

                # 等待确认终止
                try:
                    parent.wait(timeout=3)
                    if progress_callback:
                        progress_callback(f"✅ psutil终止成功 PID {pid}")
                    return
                except psutil.TimeoutExpired:
                    pass

            except psutil.NoSuchProcess:
                if progress_callback:
                    progress_callback(f"✅ 进程 PID {pid} 已不存在")
                return

        except ImportError:
            pass  # psutil不可用
        except Exception as e:
            if progress_callback:
                progress_callback(f"⚠️ psutil终止失败: {e}")

        # 方法5：最后的检查
        try:
            if process.poll() is not None:
                if progress_callback:
                    progress_callback(f"✅ 进程 PID {pid} 最终确认已终止")
                return
            else:
                if progress_callback:
                    progress_callback(f"❌ 进程 PID {pid} 仍在运行，可能需要手动终止")
        except:
            pass

    def _wait_for_process_with_monitoring(self, process, monitor_state, progress_callback, operation_name):
        """等待进程完成，同时监控进度状态"""
        import time

        stdout = None
        stderr = None

        try:
            # 如果没有监控状态，直接等待进程
            if not monitor_state:
                stdout, stderr = process.communicate()
                return stdout, stderr

            # 有监控状态时，定期检查进程和监控状态
            check_interval = 0.5  # 每0.5秒检查一次
            last_check_time = time.time()

            while True:
                current_time = time.time()

                # 检查进程是否完成
                if process.poll() is not None:
                    # 进程已完成，停止监控
                    monitor_state['should_stop'] = True

                    # 读取输出
                    try:
                        stdout, stderr = process.communicate(timeout=1)
                    except:
                        stdout, stderr = b'', b''

                    if progress_callback:
                        if process.returncode == 0:
                            progress_callback(f"✅ {operation_name}: 进程完成")
                        else:
                            progress_callback(f"❌ {operation_name}: 进程异常退出 (返回码: {process.returncode})")

                    break

                # 检查监控是否检测到错误
                if monitor_state.get('error_detected', False):
                    error_msg = monitor_state.get('error_message', '未知错误')

                    if progress_callback:
                        progress_callback(f"⚠️ {operation_name}: 检测到错误 - {error_msg}")

                    # 检查进程是否还在运行
                    if process.poll() is None:
                        if progress_callback:
                            progress_callback(f"🔄 正在终止异常进程...")

                        # 使用强力终止方法
                        self._force_terminate_process(process, progress_callback)

                        # 读取输出
                        try:
                            stdout, stderr = process.communicate(timeout=1)
                        except:
                            stdout, stderr = b'', b''
                    else:
                        # 进程已经结束，读取输出
                        try:
                            stdout, stderr = process.communicate(timeout=1)
                        except:
                            stdout, stderr = b'', b''

                    break

                # 定期输出心跳信息（每30秒）
                if current_time - last_check_time > 30:
                    if progress_callback:
                        progress_callback(f"🔄 {operation_name}: 进程运行中...")
                    last_check_time = current_time

                time.sleep(check_interval)

            return stdout, stderr

        except Exception as e:
            # 出现异常时尝试终止进程
            if progress_callback:
                progress_callback(f"💥 {operation_name}: 等待进程时出错 - {e}")

            try:
                if process.poll() is None:
                    process.terminate()
                    stdout, stderr = process.communicate(timeout=5)
            except:
                try:
                    process.kill()
                    stdout, stderr = process.communicate(timeout=5)
                except:
                    stdout, stderr = b'', b''

            return stdout, stderr

    def _parse_time_string(self, time_str):
        """解析时间字符串为秒数"""
        try:
            # 格式: HH:MM:SS.mmm
            parts = time_str.split(':')
            if len(parts) == 3:
                hours = int(parts[0])
                minutes = int(parts[1])
                seconds = float(parts[2])
                return hours * 3600 + minutes * 60 + seconds
        except Exception:
            pass
        return 0

    def compose_video_with_audio(self, video_path, audio_path, output_path,
                               subtitle_path=None, settings=None, progress_callback=None):
        """
        将音频与视频合成，可选添加字幕（不循环视频）

        参数:
            video_path (str): 视频文件路径
            audio_path (str): 音频文件路径
            output_path (str): 输出视频路径
            subtitle_path (str): 字幕文件路径（可选）
            settings (dict): 合成设置
            progress_callback (function): 进度回调函数

        返回:
            bool: 是否成功
        """
        try:
            if not self.is_available():
                raise Exception("FFmpeg不可用，请检查FFmpeg安装和配置")

            if progress_callback:
                progress_callback("开始合成视频...")

            # 创建临时目录和文件，使用TempFileManager跟踪
            if self.temp_manager:
                # 使用TempFileManager创建临时目录
                temp_dir = self.temp_manager.create_temp_dir(prefix="compose_")
            else:
                # 回退到原有逻辑
                temp_dir = self._get_temp_dir()
                if not temp_dir:
                    import tempfile
                    temp_dir = tempfile.mkdtemp()
                else:
                    import uuid
                    temp_dir = os.path.join(temp_dir, f"compose_{uuid.uuid4().hex[:8]}")
                    os.makedirs(temp_dir, exist_ok=True)

            # 创建临时文件路径
            temp_with_audio = os.path.join(temp_dir, "with_audio.mp4")
            temp_with_subtitle = os.path.join(temp_dir, "with_subtitle.mp4")

            # 如果使用TempFileManager，跟踪这些临时文件
            if self.temp_manager:
                # 预先注册这些文件路径
                for temp_file in [temp_with_audio, temp_with_subtitle]:
                    self.temp_manager.temp_files.add(temp_file)

            try:
                # 第1步：添加字幕（如果需要）
                current_video = video_path
                if subtitle_path and os.path.exists(subtitle_path) and settings and settings.get('enable_subtitle', False):
                    if progress_callback:
                        progress_callback("第1步: 添加字幕...")
                    self.add_subtitles_ffmpeg(current_video, subtitle_path, temp_with_subtitle, settings, progress_callback)
                    current_video = temp_with_subtitle

                # 第2步：合并音视频
                if progress_callback:
                    progress_callback("第2步: 合并音视频...")
                success = self.merge_audio_video(current_video, audio_path, temp_with_audio, settings, progress_callback)

                if success == "USER_STOPPED":
                    if progress_callback:
                        progress_callback("视频合成已停止")
                    return False
                elif not success:
                    raise Exception("合并音视频失败")

                # 第3步：视频编码
                self._final_encode(temp_with_audio, output_path, settings, progress_callback)

                if progress_callback:
                    progress_callback("✅ 视频合成完成")

                # 清理调试文件（如果不需要保留）
                self._cleanup_debug_files(output_path, settings)

                return True

            finally:
                # 清理临时文件
                import shutil
                try:
                    # 如果使用TempFileManager，它会自动清理跟踪的文件
                    if self.temp_manager:
                        # TempFileManager会在cleanup_all时清理这些文件
                        pass
                    else:
                        # 回退到手动清理
                        shutil.rmtree(temp_dir)
                except Exception:
                    pass

        except Exception as e:
            if progress_callback:
                progress_callback(f"❌ 视频合成失败: {str(e)}")
            print(f"视频合成错误: {e}")
            return False

    def _add_subtitles_with_software_encoder(self, video_path, subtitle_path, output_path, settings=None, progress_callback=None, _is_retry=False):
        """使用软件编码器添加字幕（硬件编码器失败时的重试方案）"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        # 强制使用软件编码器
        software_settings = settings.copy() if settings else {}
        software_settings['use_hardware_acceleration'] = False
        software_settings['encoder_choice'] = 'libx264'  # 明确指定软件编码器

        codec, ffmpeg_params = self._get_optimal_encoder_settings(software_settings)

        if progress_callback:
            progress_callback("使用软件编码器重试添加字幕...")

        try:
            # 获取视频时长用于进度计算
            try:
                video_duration = self.get_media_duration(video_path)
            except Exception:
                video_duration = None

            # 创建临时进度文件
            progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
            with open(progress_file_path, 'w') as f:
                pass  # 创建空文件

            # 构建软件编码命令
            subtitle_filter_path = subtitle_path.replace('\\', '/').replace(':', '\\:')
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file_path,
                '-i', video_path,
                '-vf', f'subtitles=filename=\'{subtitle_filter_path}\'',
                '-c:v', codec,
                '-c:a', 'copy'
            ]

            # 添加软件编码器参数
            cmd.extend(ffmpeg_params)
            cmd.append(output_path)

            # 启动FFmpeg进程
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 监控进度（重试时不显示详细进度）
            if progress_callback and video_duration and not _is_retry:
                self._monitor_ffmpeg_progress(progress_file_path, video_duration, progress_callback, "软件编码添加字幕")
            elif _is_retry and progress_callback:
                progress_callback("软件编码器重试中...")

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                # 软件编码器也失败，使用最简备用方案
                if progress_callback:
                    progress_callback("软件编码器也失败，使用最简备用方案...")

                return self._add_subtitles_fallback(video_path, subtitle_path, output_path, progress_callback, _is_retry=True)

            if progress_callback:
                progress_callback("软件编码器字幕添加完成")

            return True

        except Exception as e:
            if progress_callback:
                progress_callback(f"软件编码器字幕添加失败: {str(e)}")

            # 最后尝试备用方案
            return self._add_subtitles_fallback(video_path, subtitle_path, output_path, progress_callback, _is_retry=True)

        finally:
            # 清理临时文件
            try:
                os.unlink(progress_file_path)
            except Exception:
                pass

    def _add_subtitles_fallback(self, video_path, subtitle_path, output_path, progress_callback=None, _is_retry=False):
        """备用字幕添加方法 - 使用最简单的参数"""
        ffmpeg_exe = self._get_ffmpeg_executable()
        if not ffmpeg_exe:
            raise Exception("FFmpeg不可用")

        if progress_callback:
            if _is_retry:
                progress_callback("备用方案重试中...")
            else:
                progress_callback("使用备用方案添加字幕...")

        # 使用最简单的命令，强制软件编码
        cmd = [
            ffmpeg_exe, '-y',
            '-i', video_path,
            '-vf', f'subtitles={subtitle_path}',
            '-c:v', 'libx264',  # 强制使用软件编码器
            '-preset', 'medium',  # 使用中等预设
            '-c:a', 'copy',  # 复制音频
            output_path
        ]

        try:
            # 启动FFmpeg进程（隐藏窗口）
            import platform
            if platform.system() == "Windows":
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore',
                                         creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                         text=True, encoding='utf-8', errors='ignore')

            # 等待进程完成
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                raise Exception(f"备用字幕添加也失败: {stderr}")

            if progress_callback:
                progress_callback("备用方案字幕添加完成")

            return True

        except Exception as e:
            if progress_callback:
                progress_callback(f"备用方案失败: {str(e)}")
            raise e


    def cleanup_temp_files(self):
        """清理临时文件和缓存"""
        try:
            # 优先使用TempFileManager清理
            if self.temp_manager:
                cleaned_files, cleaned_dirs = self.temp_manager.cleanup_all()
                if cleaned_files > 0 or cleaned_dirs > 0:
                    print(f"🧹 FFmpeg合成器清理完成: {cleaned_files} 个文件, {cleaned_dirs} 个目录")
                return

            # 回退到原有清理逻辑
            # 获取系统临时目录
            temp_dir = tempfile.gettempdir()

            # 清理可能的临时文件
            temp_patterns = [
                'ffmpeg_*',
                'temp_video_*',
                'temp_audio_*',
                'temp_subtitle_*'
            ]

            import glob
            for pattern in temp_patterns:
                temp_files = glob.glob(os.path.join(temp_dir, pattern))
                for temp_file in temp_files:
                    try:
                        if os.path.isfile(temp_file):
                            # 检查文件是否是最近创建的（避免删除其他程序的文件）
                            file_age = time.time() - os.path.getctime(temp_file)
                            if file_age < 3600:  # 只删除1小时内创建的文件
                                os.unlink(temp_file)
                                print(f"清理临时文件: {temp_file}")
                    except Exception:
                        pass  # 忽略单个文件清理失败

            print("临时文件清理完成")

        except Exception:
            pass  # 忽略清理过程中的所有错误

    def _get_cache_dir(self):
        """获取缓存目录"""
        if self.cache_dir is None:
            try:
                # 优先使用项目的temp目录
                from common.path_utils import get_app_path
                app_temp_dir = os.path.join(get_app_path(), "temp")
                self.cache_dir = os.path.join(app_temp_dir, "video_composer_cache", "loop_videos")
                os.makedirs(self.cache_dir, exist_ok=True)
            except Exception:
                # 回退到系统临时目录
                import tempfile
                self.cache_dir = os.path.join(tempfile.gettempdir(), "ai_voice_loop_cache")
                os.makedirs(self.cache_dir, exist_ok=True)
        return self.cache_dir

    def _get_settings_hash(self, settings):
        """获取设置的哈希值，用于缓存键
        仅在相关开关启用时纳入对应参数，避免未勾选时更改数值导致缓存碎片化。
        同时纳入编码器/硬件开关，因为循环步骤总会编码，编码器会影响输出。"""
        if not settings:
            return "default"

        relevant_settings = {
            'custom_compression': settings.get('custom_compression', False),
            'custom_resolution': settings.get('custom_resolution', False),
            'output_format': settings.get('output_format', 'mp4'),
            'encoder': settings.get('encoder', 'auto'),
            'use_hardware_acceleration': settings.get('use_hardware_acceleration', True),
        }

        # 仅在启用自定义码率时纳入码率
        if settings.get('custom_compression', False):
            # current_compression_bitrate 优先，其次 first/other
            br = settings.get('current_compression_bitrate')
            if br is None:
                br = settings.get('first_compression_bitrate', settings.get('other_compression_bitrate', 5000))
            relevant_settings['bitrate'] = int(br) if isinstance(br, (int, float, str)) else br

        # 仅在启用自定义分辨率时纳入分辨率参数
        if settings.get('custom_resolution', False):
            relevant_settings['width'] = settings.get('width', 1920)
            relevant_settings['height'] = settings.get('height', 1080)
            relevant_settings['keep_aspect_ratio'] = settings.get('keep_aspect_ratio', True)

        # 生成简单的哈希
        import hashlib
        settings_str = str(sorted(relevant_settings.items()))
        return hashlib.md5(settings_str.encode()).hexdigest()[:8]

    def _get_cache_key(self, video_path, duration, settings):
        """生成缓存键"""
        # 使用视频文件的修改时间和大小作为标识
        try:
            stat = os.stat(video_path)
            video_id = f"{stat.st_mtime}_{stat.st_size}"
        except:
            video_id = os.path.basename(video_path)

        settings_hash = self._get_settings_hash(settings)
        duration_key = f"{duration:.1f}s"

        return f"{video_id}_{duration_key}_{settings_hash}"

    def _get_cached_loop_video(self, video_path, duration, settings):
        """检查是否有可复用的循环视频"""
        try:
            cache_key = self._get_cache_key(video_path, duration, settings)

            if cache_key in self.loop_video_cache:
                cached_path = self.loop_video_cache[cache_key]
                if os.path.exists(cached_path):
                    return cached_path
                else:
                    # 缓存文件不存在，清理缓存项
                    del self.loop_video_cache[cache_key]

            return None
        except Exception as e:
            print(f"检查缓存失败: {e}")
            return None

    def _cache_loop_video(self, video_path, duration, temp_video_path, settings):
        """缓存循环视频"""
        try:
            cache_key = self._get_cache_key(video_path, duration, settings)
            cache_dir = self._get_cache_dir()

            # 生成缓存文件路径
            cache_filename = f"loop_{cache_key}.mp4"
            cache_path = os.path.join(cache_dir, cache_filename)

            # 复制临时文件到缓存目录
            shutil.copy2(temp_video_path, cache_path)

            # 更新缓存记录
            self.loop_video_cache[cache_key] = cache_path

            print(f"缓存循环视频: {cache_filename}")

        except Exception as e:
            print(f"缓存循环视频失败: {e}")

    def _reuse_loop_video_with_new_frame(self, cached_video, original_video, output_path, settings, progress_callback):
        """复用循环视频但使用原视频重新开始（强制关键帧确保跳转）"""
        try:
            if progress_callback:
                progress_callback("正在复用循环视频并重新开始...")

            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return False

            # 获取缓存视频的总时长
            cached_duration = self.get_media_duration(cached_video)

            # 获取原视频的时长（单个循环的长度）
            original_duration = self.get_media_duration(original_video)

            if progress_callback:
                progress_callback(f"缓存视频时长: {cached_duration:.1f}秒，原视频循环: {original_duration:.1f}秒")

            # 创建临时进度文件
            progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
            with open(progress_file_path, 'w') as f:
                pass  # 创建空文件

            # 检查是否需要重新编码以确保关键帧
            need_encoding = self._need_video_encoding_for_loop(settings)

            if need_encoding:
                # 需要重新编码，强制关键帧间隔
                if progress_callback:
                    progress_callback("使用重新编码模式确保关键帧...")

                cmd = [
                    ffmpeg_exe, '-y',
                    '-progress', progress_file_path,
                    '-stream_loop', '-1',  # 无限循环原视频
                    '-i', original_video,
                    '-t', str(cached_duration),  # 裁剪到与缓存视频相同的时长
                    '-c:v', 'libx264',
                    '-preset', 'fast',
                    '-crf', '23',
                    '-g', '30',  # 强制GOP大小为30帧（约1秒间隔）
                    '-keyint_min', '30',  # 最小关键帧间隔
                    '-force_key_frames', 'expr:gte(t,n_forced*1)',  # 每秒强制一个关键帧
                    '-pix_fmt', 'yuv420p',
                    '-c:a', 'aac',
                    '-b:a', '128k',
                    '-movflags', '+faststart',
                    output_path
                ]
            else:
                # 尝试直接复制，但添加关键帧处理
                if progress_callback:
                    progress_callback("使用直接复制模式...")

                cmd = [
                    ffmpeg_exe, '-y',
                    '-progress', progress_file_path,
                    '-stream_loop', '-1',  # 无限循环原视频
                    '-i', original_video,
                    '-t', str(cached_duration),  # 裁剪到与缓存视频相同的时长
                    '-c', 'copy',  # 直接复制
                    '-avoid_negative_ts', 'make_zero',
                    '-fflags', '+genpts+igndts',  # 重新生成时间戳并忽略DTS
                    '-movflags', '+faststart',
                    output_path
                ]

            # 执行FFmpeg命令
            process = create_managed_ffmpeg_process(cmd)

            # 监控进度
            try:
                cached_duration = self.get_media_duration(cached_video)
                self._monitor_ffmpeg_progress(progress_file_path, cached_duration, progress_callback, "重新生成循环")
            except Exception:
                pass

            process.wait()
            cleanup_managed_process(process)

            # 清理进度文件
            try:
                os.unlink(progress_file_path)
            except:
                pass

            if process.returncode == 0 and os.path.exists(output_path):
                if progress_callback:
                    progress_callback("循环视频重新生成完成（优化跳转能力）")
                return True
            elif not need_encoding:
                # 如果直接复制失败，尝试重新编码
                if progress_callback:
                    progress_callback("直接复制失败，尝试重新编码...")
                return self._reuse_loop_video_with_encoding(cached_video, original_video, output_path, settings, progress_callback)
            else:
                if progress_callback:
                    progress_callback("循环视频重新生成失败")
                return False

        except Exception as e:
            if progress_callback:
                progress_callback(f"循环视频重新生成失败: {e}")
            return False

    def _reuse_loop_video_with_encoding(self, cached_video, original_video, output_path, settings, progress_callback):
        """使用重新编码方式复用循环视频（确保关键帧间隔）"""
        try:
            if progress_callback:
                progress_callback("使用重新编码方式确保跳转能力...")

            ffmpeg_exe = self._get_ffmpeg_executable()
            if not ffmpeg_exe:
                return False

            # 获取缓存视频的总时长
            cached_duration = self.get_media_duration(cached_video)

            # 创建临时进度文件
            progress_file_path = self._create_temp_file(suffix='.txt', prefix='progress_')
            with open(progress_file_path, 'w') as f:
                pass  # 创建空文件

            # 使用重新编码，强制规律的关键帧间隔
            cmd = [
                ffmpeg_exe, '-y',
                '-progress', progress_file_path,
                '-stream_loop', '-1',  # 无限循环原视频
                '-i', original_video,
                '-t', str(cached_duration),  # 裁剪到与缓存视频相同的时长
                '-c:v', 'libx264',
                '-preset', 'fast',  # 快速编码
                '-crf', '23',  # 合理的质量
                '-g', '30',  # GOP大小30帧（约1秒@30fps）
                '-keyint_min', '30',  # 最小关键帧间隔
                '-sc_threshold', '0',  # 禁用场景切换检测
                '-force_key_frames', 'expr:gte(t,n_forced*1)',  # 每秒强制一个关键帧
                '-pix_fmt', 'yuv420p',  # 兼容性最好的像素格式
                '-c:a', 'aac',
                '-b:a', '128k',
                '-movflags', '+faststart',  # 优化播放
                output_path
            ]

            if progress_callback:
                progress_callback("重新编码中，强制关键帧间隔...")

            # 执行FFmpeg命令
            process = create_managed_ffmpeg_process(cmd)

            # 监控进度
            try:
                cached_duration = self.get_media_duration(cached_video)
                self._monitor_ffmpeg_progress(progress_file_path, cached_duration, progress_callback, "重新编码")
            except Exception:
                pass

            process.wait()
            cleanup_managed_process(process)

            # 清理进度文件
            try:
                os.unlink(progress_file_path)
            except:
                pass

            if process.returncode == 0 and os.path.exists(output_path):
                if progress_callback:
                    progress_callback("重新编码完成，关键帧间隔已优化")
                return True
            else:
                if progress_callback:
                    progress_callback("重新编码失败")
                return False

        except Exception as e:
            if progress_callback:
                progress_callback(f"重新编码失败: {e}")
            return False

    def cleanup_cache(self, max_age_hours=24, max_files=50):
        """清理过期的缓存文件"""
        try:
            cache_dir = self._get_cache_dir()
            if not os.path.exists(cache_dir):
                return

            current_time = time.time()
            max_age_seconds = max_age_hours * 3600

            # 获取所有缓存文件
            cache_files = []
            for filename in os.listdir(cache_dir):
                if filename.startswith("loop_") and filename.endswith(".mp4"):
                    filepath = os.path.join(cache_dir, filename)
                    try:
                        stat = os.stat(filepath)
                        cache_files.append((filepath, stat.st_mtime, stat.st_size))
                    except:
                        continue

            # 按修改时间排序（最新的在前）
            cache_files.sort(key=lambda x: x[1], reverse=True)

            files_deleted = 0

            # 删除过期文件
            for filepath, mtime, size in cache_files:
                file_age = current_time - mtime

                # 如果文件过期或者文件数量超过限制
                if file_age > max_age_seconds or len(cache_files) - files_deleted > max_files:
                    try:
                        os.unlink(filepath)
                        files_deleted += 1

                        # 从内存缓存中移除
                        cache_key_to_remove = None
                        for key, path in self.loop_video_cache.items():
                            if path == filepath:
                                cache_key_to_remove = key
                                break

                        if cache_key_to_remove:
                            del self.loop_video_cache[cache_key_to_remove]

                    except Exception as e:
                        print(f"删除缓存文件失败 {filepath}: {e}")

            if files_deleted > 0:
                print(f"清理了 {files_deleted} 个过期的循环视频缓存文件")

        except Exception as e:
            print(f"清理缓存失败: {e}")

        except Exception as e:
            print(f"清理临时文件时出错: {e}")
            pass  # 忽略清理过程中的所有错误
